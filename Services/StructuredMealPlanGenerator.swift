import Foundation

/// V6: Slot-based Meal Plan generator.
/// Generates recipes per meal type and assigns them deterministically into (date × meal) slots.
actor StructuredMealPlanGenerator {
    private let adapter: RecipeServiceAdapter
    private let pantryService: PantryService
    private let authService: AuthenticationService
    private let cutoffManager: MealCutoffManager

    init(adapter: RecipeServiceAdapter,
         pantryService: PantryService,
         authService: AuthenticationService,
         cutoffManager: MealCutoffManager = MealCutoffManager()) {
        self.adapter = adapter
        self.pantryService = pantryService
        self.authService = authService
        self.cutoffManager = cutoffManager
    }

    /// Generate a structured plan with exact (date, meal) assignments.
    /// - Returns: `MealPlan` where each `MealSlot` corresponds to a concrete date and meal.
    func generatePlan(_ request: MealPlanGenerationRequest, now: Date = Date()) async throws -> MealPlan {
        // 0) Basic input validation and early exits (graceful handling)
        // - Selected meals must not be empty
        // - Pantry must have items
        // - Enumerated slots must be non-empty (date window + cutoffs)
        let pantryCount = await pantryService.pantryItems.count
        guard !request.selectedMeals.isEmpty else { return MealPlan(days: []) }
        guard pantryCount > 0 else { return MealPlan(days: []) }

        // 1) Enumerate slot requests within 1..7 days window and skip same-day passed meals
        let slots = enumerateSlots(request: request, now: now)
        if slots.isEmpty { return MealPlan(days: []) }

        // 2) Aggregate total dishes needed per meal type across all days
        var totalPerMeal: [MealType: Int] = [:]
        for s in slots { totalPerMeal[s.mealType, default: 0] += s.count }

        // 3) Generate per-meal batches via existing adapter, then assign sequentially to slots
        var generatedByMeal: [MealType: [RecipeUIModel]] = [:]

        for (meal, totalCount) in totalPerMeal {
            try Task.checkCancellation()
            guard let cfg = request.slotConfigs[meal] else { continue }
            let cappedCount = min(totalCount, 12)
            guard cappedCount > 0 else {
                generatedByMeal[meal] = []
                continue
            }
            // Build a request to generate a single meal type with the capped dish count
            let details = RequestDetails.singleMeal(
                mealType: meal,
                count: cappedCount,
                maxTime: cfg.cookingTimeMinutes,
                cuisines: request.cuisines,
                additionalRequest: request.additionalRequest,
                equipmentOwned: request.equipmentOwned
            )
            let genReq = RecipeGenerationRequest(mode: .custom, pantryItemCount: pantryCount, requestDetails: details)

            // Use adapter to respect profile + pantry enforcement
            // Wrap in do/catch to ensure per-meal failures don't crash the whole plan
            do {
                let uiModels = try await adapter.generate(
                    using: genReq,
                    cookingTimeMinutes: cfg.cookingTimeMinutes,
                    authService: authService,
                    prefetchDetails: false
                )
                generatedByMeal[meal] = uiModels
            } catch {
                if error is CancellationError {
                    throw error
                }
                // Graceful degradation: record empty results for this meal
                // Errors will be surfaced to UI via higher-level state, but plan generation continues
                print("❌ Failed to generate \(meal): \(error)")
                generatedByMeal[meal] = []
            }
        }

        // 4) Assign to concrete (date × meal) slots in day-major order
        let days = buildDayPlans(from: slots, pool: &generatedByMeal)
        return MealPlan(days: days)
    }

    // MARK: - Slot enumeration with same-day cutoffs

    private func enumerateSlots(request: MealPlanGenerationRequest, now: Date) -> [MealSlotRequest] {
        let calendar = Calendar.current
        let start = calendar.startOfDay(for: request.startDate)
        let todayStart = calendar.startOfDay(for: now)
        let validRange = MealPlanDateRange.validDateRange(from: now, calendar: calendar)
        let maxEnd = validRange.upperBound
        let selectedMeals = request.selectedMeals
        guard !selectedMeals.isEmpty else { return [] }

        var result: [MealSlotRequest] = []
        let maxDays = max(1, request.days)
        for idx in 0..<maxDays {
            guard let date = calendar.date(byAdding: .day, value: idx, to: start) else { continue }

            // Enforce global date window: skip past; stop beyond today+7
            if date < todayStart { continue }
            if date > maxEnd { break }

            for meal in selectedMeals {
                let cfg = request.slotConfigs[meal] ?? MealConfig()
                // Apply same-day cutoff only if the slot's date is today
                if cutoffManager.shouldSkipSameDay(meal: meal, on: date, now: now, calendar: calendar) {
                    continue
                }
                let count = max(1, min(6, cfg.numberOfDishes))
                result.append(MealSlotRequest(date: date, dayIndex: idx, mealType: meal, count: count))
            }
        }
        return result
    }

    // MARK: - Assignment helpers

    private func buildDayPlans(from slots: [MealSlotRequest], pool: inout [MealType: [RecipeUIModel]]) -> [DayPlan] {
        // Group slots by dayIndex → meal ordering
        var byDay: [Int: [(meal: MealType, count: Int, date: Date)]] = [:]
        for s in slots { byDay[s.dayIndex, default: []].append((s.mealType, s.count, s.date)) }

        let sortedDayIndices = byDay.keys.sorted()
        var dayPlans: [DayPlan] = []
        for dayIndex in sortedDayIndices {
            let entries = byDay[dayIndex] ?? []
            // Sort meals consistently (Breakfast, Lunch, Dinner)
            let sortedMeals = entries.sorted { lhs, rhs in lhs.meal.sortKey < rhs.meal.sortKey }
            var mealSlots: [MealSlot] = []
            for entry in sortedMeals {
                let uiForMeal = pool[entry.meal] ?? []
                let take = min(entry.count, uiForMeal.count)
                let picked = Array(uiForMeal.prefix(take))
                pool[entry.meal] = Array(uiForMeal.dropFirst(take))
                // If adapter returned fewer than needed, we stop at available count
                for model in picked {
                    let adjusted = model.with(dayIndex: dayIndex, scheduledDate: entry.date, mealType: entry.meal)
                    let slot = MealSlot(slotId: UUID(), dayIndex: dayIndex, mealType: entry.meal, recipe: adjusted)
                    mealSlots.append(slot)
                }
            }
            // Use first slot's date or fallback
            let date = entries.first?.date ?? Date()
            dayPlans.append(DayPlan(date: date, meals: mealSlots))
        }
        return dayPlans
    }
}

private extension MealType {
    var sortKey: Int {
        switch self { case .breakfast: return 0; case .lunch: return 1; case .dinner: return 2 }
    }
}

private extension RecipeUIModel {
    func with(dayIndex: Int?, scheduledDate: Date?, mealType: MealType?) -> RecipeUIModel {
        RecipeUIModel(
            id: self.id,
            title: self.title,
            subtitle: self.subtitle,
            estimatedTime: self.estimatedTime,
            imageURL: self.imageURL,
            ingredientsFromPantry: self.ingredientsFromPantry,
            additionalIngredients: self.additionalIngredients,
            difficulty: self.difficulty,
            mealType: mealType ?? self.mealType,
            dayIndex: dayIndex,
            servings: self.servings,
            cuisine: self.cuisine,
            scheduledDate: scheduledDate
        )
    }
}
