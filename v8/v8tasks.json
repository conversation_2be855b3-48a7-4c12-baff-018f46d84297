{"project": "V8 Meal Plan Generation Implementation", "description": "Critical stability fixes and performance optimizations for meal plan generation based on v8/meal-plan-generation-prd-unified.md", "environment": {"language": "Swift", "swift_version": "5.9", "platform": "iOS", "deployment_target": "17.0", "ui_framework": "SwiftUI", "concurrency": "Swift Concurrency (async/await, MainActor)", "build_system": "XcodeGen (project.yml)"}, "phases": [{"phase": "MVP", "description": "Critical Stability & Core Features (Days 1-3)", "tasks": [{"id": "MVP-1", "title": "Start Date Selection & Enumeration", "description": "Add userSelectedStartDate tracking and start date picker UI", "priority": "P0", "day": 1, "acceptance_criteria": ["Start date picker bounded to today...today+7", "Same-day cutoffs honored (meals after cutoff skipped)", "Enumeration begins from selected date"], "files_to_modify": ["Features/RecipeGenerator/RecipeGeneratorView.swift", "Features/RecipeGenerator/RecipeGeneratorViewModel.swift", "Services/RecipeRequestBuilder.swift"], "implementation_notes": "Add DatePicker with helper text 'Start generating from this date'. Thread startDate through MealPlanGenerationRequest.", "prd_reference": "FR-MVP-1, Section 5", "status": "completed"}, {"id": "MVP-2", "title": "Append-Only Merge Policy", "description": "Implement AppendAll merge policy for overlapping days", "priority": "P0", "day": 1, "acceptance_criteria": ["Overlapping days preserve existing items", "New items appear alongside existing ones", "No replacements occur in MVP"], "files_to_modify": ["Services/PlanStore.swift", "Features/RecipeGenerator/RecipeGeneratorViewModel.swift"], "implementation_notes": "Do not change global default. From Meal Plan save path call PlanStore.mergeAndSave(..., policy: .appendAll). PlanStore computes OverlapSaveSummary with addedCountsByDayMeal.", "prd_reference": "FR-MVP-2, Section 5", "status": "completed"}, {"id": "MVP-3", "title": "Disable Prefetch for Meal Plan", "description": "Add prefetchDetails parameter and disable for structured path", "priority": "P0", "day": 1, "acceptance_criteria": ["Meal Plan performs zero detail prefetch calls", "Quick mode unchanged (still prefetches)", "Plan content and slot assignment unchanged"], "files_to_modify": ["Services/RecipeServiceAdapter.swift", "Services/StructuredMealPlanGenerator.swift"], "implementation_notes": "Add prefetchDetails: Bool = true parameter. Guard prefetch block with flag. Call with prefetchDetails: false from StructuredMealPlanGenerator.", "prd_reference": "FR-MVP-7, Section 5", "status": "completed"}, {"id": "MVP-4", "title": "Per-Meal Error Isolation & Resource Cap", "description": "Add robust error handling and prevent crashes", "priority": "P0", "day": 2, "acceptance_criteria": ["Complex configs (3 days; B2/L3/D4) complete without crash", "Per-meal failures don't crash entire run", "Resource cap at 12 dishes per meal enforced"], "files_to_modify": ["Services/StructuredMealPlanGenerator.swift"], "implementation_notes": "Wrap per-meal loop in try/catch. Cap count with min(total, 12). Add Task.checkCancellation(). Log failures but continue with empty array.", "prd_reference": "FR-MVP-5, Section 4A Issue 1, Section 9A code snippets", "status": "completed"}, {"id": "MVP-5", "title": "Generation Timeout Protection", "description": "Add timeout helper with bounded nanoseconds conversion", "priority": "P0", "day": 2, "acceptance_criteria": ["Generation completes within 45s or times out gracefully", "Bounded nanoseconds conversion (1ms to 10min)", "TimeoutError thrown when exceeded"], "files_to_modify": ["Utilities/AsyncTimeout.swift", "Features/RecipeGenerator/RecipeGeneratorViewModel.swift"], "implementation_notes": "Create withTimeout helper function. Wrap generateStructuredMealPlan call with timeout. Handle TimeoutError gracefully.", "prd_reference": "FR-MVP-5, Section 9A timeout helper code", "status": "completed"}, {"id": "MVP-6", "title": "Configuration Change Detection", "description": "Implement generation fingerprinting to prevent stale navigation", "priority": "P0", "day": 2, "acceptance_criteria": ["Configuration changes trigger actual generation", "Navigation only after successful fresh generation", "No jumps to previous results when config changes"], "files_to_modify": ["Models/GenerationFingerprint.swift", "Features/RecipeGenerator/RecipeGeneratorViewModel.swift"], "implementation_notes": "Create GenerationFingerprint struct with selectedMeals, days, mealConfigs, cuisines, additionalRequest. Compare before generation. Store after success.", "prd_reference": "FR-MVP-8, Section 4A Issue 2, Section 9A fingerprint code", "status": "completed"}, {"id": "MVP-7", "title": "Non-Blocking Post-Save Summary", "description": "Show inline banner with generation summary", "priority": "P0", "day": 2, "acceptance_criteria": ["Summary shows correct counts by day×meal", "View CTA navigates to relevant week", "Auto-dismiss in 3-5 seconds"], "files_to_modify": ["Features/RecipeGenerator/RecipeGeneratorViewModel.swift"], "implementation_notes": "Present banner after save using OverlapSaveSummary. Format: 'Added: Mon Lunch ×2, <PERSON>e Dinner ×1. View'. Add View CTA navigation.", "prd_reference": "FR-MVP-3, Section 7", "status": "completed"}, {"id": "MVP-8", "title": "Fix Manage Button Integration", "description": "Standardize date indexing for proper slot collection", "priority": "P0", "day": 2, "acceptance_criteria": ["Manage displays generated items immediately after generation", "Delete/favorite/share operations succeed", "Date keys use yyyy-MM-dd, UTC, en_US_POSIX format"], "files_to_modify": ["Views/SwipeableMealPlanCalendarView.swift"], "implementation_notes": "Ensure planIndex uses consistent date formatting. Add fallback slot collection from plan.days. Debug slotsProvider logic.", "prd_reference": "FR-MVP-4, Section 4A Issue 3", "status": "completed"}, {"id": "MVP-9", "title": "Prompt Fidelity - All Configs as Constraints", "description": "Include all user configurations in AI prompts", "priority": "P0", "day": 3, "acceptance_criteria": ["Prompts include meal type guidance", "Target dish count reflected in prompts", "Cuisines and additional request included when provided", "Adapter trims/expands to requested N post-hoc"], "files_to_modify": ["Services/RecipeServiceAdapter.swift", "Services/RecipeGenerationService.swift", "Models/Recipe.swift"], "implementation_notes": "Add targetMealType and targetDishCount to RecipePreferences. Set from adapter per batch. Ensure cuisines/additional propagate from request to preferences. Update createRecipePrompt to include meal guidance, dish count, cuisines, and additional request.", "prd_reference": "FR-MVP-6"}, {"id": "MVP-10", "title": "Unit Tests - Core Functionality", "description": "Implement unit tests for MVP features", "priority": "P0", "day": 3, "acceptance_criteria": ["Fingerprint comparison tests pass", "Prefetch flag tests validate Meal Plan vs Quick behavior", "Error isolation tests prevent crash propagation", "Prompt tests validate meal type, dish count, cuisines, additional request lines"], "files_to_modify": ["Tests/MealPlanGenerationTests.swift"], "implementation_notes": "Test fingerprint generation/comparison, prefetch flag behavior, per-meal error isolation, timeout helper, date indexing round-trip, and prompt content (using a prompt builder spy/test double).", "prd_reference": "Section 11A Unit Tests"}]}, {"phase": "Performance", "description": "Performance & Responsiveness Optimizations (Days 4-6)", "tasks": [{"id": "PERF-1", "title": "Validate Prefetch Disable Impact", "description": "Comprehensive validation that prefetch disable works correctly", "priority": "P1", "day": 4, "acceptance_criteria": ["Meal Plan path confirmed to perform 0 prefetch calls", "Quick mode still prefetches details", "Detail views load on demand without issues"], "files_to_modify": ["Tests/RecipeServiceAdapterTests.swift"], "implementation_notes": "Add spy/mock tests to verify prefetch behavior. Test detail view on-demand loading. Smoke test recipe detail screens.", "prd_reference": "FR-PERF Disable Detail Prefetch, Day 4 milestone"}, {"id": "PERF-2", "title": "Parallelize Per-Meal Generation", "description": "Use TaskGroup to run meal types concurrently", "priority": "P1", "day": 5, "acceptance_criteria": ["Wall-clock time ≈ max(single meal batch time), not sum", "No data races in concurrent generation", "Concurrency cap of 2-3 if needed for rate limits"], "files_to_modify": ["Services/StructuredMealPlanGenerator.swift"], "implementation_notes": "Replace serial per-meal loop with TaskGroup. Run batches concurrently with small cap. Maintain error isolation per meal type.", "prd_reference": "FR-PERF Parallelize Per-Meal Generation"}, {"id": "PERF-3", "title": "MainActor Responsiveness", "description": "Remove MainActor blocking during generation", "priority": "P1", "day": 5, "acceptance_criteria": ["UI remains responsive during generation", "Cancel reacts within 500ms", "No MainActor blocking on full generation task"], "files_to_modify": ["Features/RecipeGenerator/RecipeGeneratorViewModel.swift"], "implementation_notes": "Remove await task.value on MainActor. Update viewState on MainActor only. Keep heavy work off main thread.", "prd_reference": "FR-PERF Keep MainActor Responsive"}, {"id": "PERF-4", "title": "PlanStore Background Processing", "description": "Offload merge/encode operations off MainActor", "priority": "P1", "day": 6, "acceptance_criteria": ["No observable main thread hitch during save", "Merge and JSON encoding on background thread", "Final write remains atomic on MainActor"], "files_to_modify": ["Services/PlanStore.swift"], "implementation_notes": "Move merge/encode to background task. Use await MainActor.run for final UserDefaults write. Preserve atomicity and overlap semantics.", "prd_reference": "FR-PERF Offload Heavy Work"}, {"id": "PERF-5", "title": "Integration Tests - Performance Scenarios", "description": "Validate performance improvements with real scenarios", "priority": "P1", "day": 6, "acceptance_criteria": ["AI calls drop from ≤10 to 4 in Lunch+Dinner scenario", "Wall-clock time decreases significantly", "Cancel responsiveness <500ms validated"], "files_to_modify": ["Tests/MealPlanPerformanceTests.swift"], "implementation_notes": "Test 5-day Lunch+Dinner scenario. Measure AI call counts, wall-clock times. Validate cancel interruption timing.", "prd_reference": "Section 11A Integration Tests, Performance Scenario"}]}], "validation_scenarios": [{"name": "Crash Prevention", "description": "3 days; Breakfast 2 + Lunch 3 + Dinner 4 dishes", "expected_outcome": "Completes without crash, partial failures tolerated"}, {"name": "Configuration Change Detection", "description": "Generate 1 breakfast → change to 2 breakfast → generate", "expected_outcome": "New generation occurs, spinner visible, no stale results"}, {"name": "Overlap Append", "description": "Generate days 1-7 → generate days 5-9", "expected_outcome": "Days 5-7 have appended items, days 8-9 are new"}, {"name": "Performance Improvement", "description": "5 days, Lunch 3×@40min, Dinner 4×@60min", "expected_outcome": "AI calls drop ≤10 → 4, 50%+ faster wall-clock time"}, {"name": "Manage Integration", "description": "Generate plan → open Plans → tap Manage", "expected_outcome": "Generated recipes visible, delete/favorite/share work"}, {"name": "Start Date & Same-day Cutoffs", "description": "Pick today near cutoff; generate", "expected_outcome": "Meals before cutoff are skipped; enumeration begins at selected date"}], "key_metrics": {"ai_calls_reduction": "≤10 → 4 for Lunch+Dinner scenario", "perceived_wait_reduction": "≥50% improvement", "cancel_responsiveness": "<500ms", "crash_rate": "0% for complex configurations", "manage_functionality": "100% success rate for operations"}, "implementation_notes": {"phase_dependencies": "MVP must complete before Performance phase", "backward_compatibility": "Quick mode behavior unchanged throughout", "rollout_strategy": "Feature flags for parallelization, prefetch default true, fingerprint behavior gate (equal-fingerprint reuse vs force regenerate)", "monitoring_required": ["generation_started/completed timings", "ai_calls_total counts", "fingerprint_match/mismatch events", "timeout/error rates"]}, "prd_reference": "All tasks reference v8/meal-plan-generation-prd-unified.md sections as specified in prd_reference fields"}