// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		0284549EC8FD2A757D22C0FB /* FirebaseFirestore in Frameworks */ = {isa = PBXBuildFile; productRef = B4B07A517E72BCF7376D449A /* FirebaseFirestore */; };
		04F4BC4DFB7E9DCA9E050276 /* RecipeDetailCache.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD5874C638A51E908AA3C197 /* RecipeDetailCache.swift */; };
		076E088D06AEA68BF880E443 /* EquipmentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBB03341FB7787DBE4C01335 /* EquipmentView.swift */; };
		07CA588ECA8D0CC719AEE696 /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = D59E35DCDAE975A10BC98623 /* FirebaseAnalytics */; };
		097F1718E88068E43C52852C /* NavigationCoordinatorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A8BF948EA96EBF61EA2C4D8E /* NavigationCoordinatorTests.swift */; };
		0CC8C18E102CE54D96BE07F2 /* WeeklyMealPlan.swift in Sources */ = {isa = PBXBuildFile; fileRef = 72BDAC489562E49EF25728FB /* WeeklyMealPlan.swift */; };
		0CCE064221711B63365F6960 /* SwipeableMealPlanCalendarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1713B0A4BB0475F6E1513321 /* SwipeableMealPlanCalendarView.swift */; };
		0D87B0BAB324F3C46E9A549F /* CollapsedCalendarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 38C4E523471A626055C6E95E /* CollapsedCalendarView.swift */; };
		103EBCFA20A105DBA29F06BA /* QuickEmptyStateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F5D207A9E7FF3219859AD0FC /* QuickEmptyStateView.swift */; };
		10DB2E5C2CC15BC608773B0B /* RecipeGrouper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 561B59FB1C991C51A3AF05DB /* RecipeGrouper.swift */; };
		10DD97B2DD408A1AB1CA7FA2 /* QuickHistoryManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7A3DBA7A6E753996362EB552 /* QuickHistoryManager.swift */; };
		11078A23C9E7C54B3BE9DD28 /* CustomItemEntrySheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 119110C234FA4B3A7A158FBC /* CustomItemEntrySheet.swift */; };
		11C58082195D9D6D63494CFC /* GroupedResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51F07E8F5B2F774E45C5AD58 /* GroupedResultsView.swift */; };
		11F4D8DE14658CEE89A81B24 /* RegenerateRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 416FE6AD741760160A09D36A /* RegenerateRequest.swift */; };
		1390FD32E792A0A9719D78FC /* MealPlanDateRange.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A2A3C01702A9D29F93ECE9D /* MealPlanDateRange.swift */; };
		13C3BFA5787D126B76F9B222 /* DaysSelector.swift in Sources */ = {isa = PBXBuildFile; fileRef = A8BE75E54566F07167996995 /* DaysSelector.swift */; };
		1602CCC1FCD042DA353BFB5D /* ErrorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65C9D20B21C9792C0477E9CB /* ErrorView.swift */; };
		16947ACBFFE296C4C5608521 /* ImagePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = C093F1FD032970F0D124F474 /* ImagePicker.swift */; };
		190112BDFD568D7BD1682DCA /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 3C4267156C55BCA053A6D391 /* Assets.xcassets */; };
		1A3D422E366B31A3F4C92CA7 /* PantryStateWarning.swift in Sources */ = {isa = PBXBuildFile; fileRef = C0F436AB8DE0B60A131EAA60 /* PantryStateWarning.swift */; };
		1C514D8C68BD8C0DA425F4B0 /* ConcurrencyManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 618BC01DC46E43C192A972EA /* ConcurrencyManager.swift */; };
		1E3C025D2108AC511947205F /* ConfigurationSummary.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0AE66C6EA17D96111A50370 /* ConfigurationSummary.swift */; };
		1F8E6BE803A8940A5599CAC2 /* CuisinesChips.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C7CD7EB90744FA9A3D485D6 /* CuisinesChips.swift */; };
		2009917FD1AE77EF5C26151D /* PantryViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = D94EBA0AA79E38BB545F9F61 /* PantryViewModel.swift */; };
		20D130C30EEC632A98A79BA2 /* RecipeGrouperTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEEFC3C7A57736C5A6735DB9 /* RecipeGrouperTests.swift */; };
		22A148ED2EDA0D9F1398D8EB /* NameCanonicalizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5034CD8F3B40B2B21EB5A035 /* NameCanonicalizer.swift */; };
		257DCFADF4F8AA5375CEAAE6 /* QuickResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 203CBC78A5DD35F560757709 /* QuickResultsView.swift */; };
		25CEE0B18FF5D33FBB0A3CDA /* MealPlan.swift in Sources */ = {isa = PBXBuildFile; fileRef = 269ABED48A4B361CEFD34F9B /* MealPlan.swift */; };
		286D3AD60207B07E1922ED46 /* Recipe.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE22904B43C38F5DA2C1BCCE /* Recipe.swift */; };
		2CD4FB43C1F24D0A6C270602 /* RecipeUIModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00C6D31A5B45C429D2C7BC73 /* RecipeUIModel.swift */; };
		2EFBF9ADB301832F959B5675 /* DoNotEatHelpSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = CDDDEC1AA2F6B5C324E8D90D /* DoNotEatHelpSheet.swift */; };
		300FF772D541C1E8D839037D /* ResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58F7E951E15E903C3F2DA88E /* ResultsView.swift */; };
		305107201B6848AF340565CE /* ExpandedCalendarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9A4AB7496EBE832DB8795330 /* ExpandedCalendarView.swift */; };
		331CC5F75E97D39B88BD021E /* GenerationFingerprint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74A0B7153B64EF215DE57ABB /* GenerationFingerprint.swift */; };
		332DCFF043824410C8B1D0CB /* ReauthenticationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 254B55743876E6E92DCC2F22 /* ReauthenticationView.swift */; };
		34B6D7AA331365DFD6C0948F /* AccountResetManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1FAE924D1A86CB90B9B39DE0 /* AccountResetManager.swift */; };
		350020676BBFA2E592C4CE7A /* AuthenticationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 002990977B33054F36A3499D /* AuthenticationService.swift */; };
		397983BFA3B1C61BAAEA98ED /* QuickResultCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65151B8943DDA722BFA8A88F /* QuickResultCardView.swift */; };
		39A499A7CDD24F1909081748 /* RecipeCardHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B356A9042F89FA0965EF0447 /* RecipeCardHeaderView.swift */; };
		3AFFA6126A9555091B68EAC6 /* SwiftDataModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = D4867FFC3C03EA927A2A4619 /* SwiftDataModels.swift */; };
		3BBAF884691CE8BCAC30AFE1 /* RecipesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98B8CC92CB9B5D9E94B23193 /* RecipesView.swift */; };
		3BF985B6E33165CCCAB97988 /* MultiImagePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11DBEC63A8F70F25F4C5480C /* MultiImagePicker.swift */; };
		3CEEAA16C0A38AD1F62E30E0 /* PantryService.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFC23B81C1836632A9C0C7D1 /* PantryService.swift */; };
		3D85C450419AD2C6B5F04794 /* ManageSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D4E1A6DF07EA911BE766638A /* ManageSelectionView.swift */; };
		3D9558C8668010FB44D3979D /* RetentionPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = BAC67331A1B12A7A1522632C /* RetentionPolicy.swift */; };
		3E55F8A163B930B0C10DF7C3 /* ExpirationNotificationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B0B1D1EECDE31EBE316148E /* ExpirationNotificationManager.swift */; };
		3F5F6048E048F4323F9806C6 /* DoNotEatHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 36DC5C478C9FA9ECC7F0D47F /* DoNotEatHelper.swift */; };
		405B7FF8557B6E9D50708FB4 /* ViewState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 35CDF50BB12C724D6BBE8CC2 /* ViewState.swift */; };
		40FEB17B88C5F3765C569E97 /* RemoteConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FD4633A895C41348C22C35D /* RemoteConfiguration.swift */; };
		41B88EA8A705136D011C3957 /* QuickHistoryIndexEntry.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1F608E2C70FC1D3D0EFE40EA /* QuickHistoryIndexEntry.swift */; };
		422BFD7C7D9D1FC11CE51160 /* AsyncTimeoutTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B1553BA82F6EB30E6E2C6CB /* AsyncTimeoutTests.swift */; };
		4240ECF855067C42906AAAAC /* TelemetryFeedbackTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4EAA0C9279CD7A5A60C41D4C /* TelemetryFeedbackTests.swift */; };
		4364C48E93AFE8B0D52F6AA5 /* DoNotEatHelperTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7A884A291FA4749F093A2A99 /* DoNotEatHelperTests.swift */; };
		4455621856F29081BB21E37C /* RecipeHistoryTabView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4418C6EBABFCD397C6708DB9 /* RecipeHistoryTabView.swift */; };
		456E8CB6D048D357117FDAE1 /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 44C234B0CF86ED71B89397C0 /* LoadingView.swift */; };
		457A708AE6D7984C1639692F /* SignInManualTest.md in Resources */ = {isa = PBXBuildFile; fileRef = CF9EE15071AC0CEEA67DC8EA /* SignInManualTest.md */; };
		45D2DE7DBB798692C6F82957 /* SignInView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FFE3881D55D069B1B730E46 /* SignInView.swift */; };
		45DC5B2DB5B5996098079705 /* FavoriteItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7BC9FDEE48F6CEFBC05DBDB3 /* FavoriteItem.swift */; };
		465AA90B58DBF295DF9729FB /* PantryCategory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 84B3CC06B1FB6E25E979F9D9 /* PantryCategory.swift */; };
		46D1AA5FBF4641ECC38CAB93 /* GeneratedRecipeDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2584E995A79858BB53E06153 /* GeneratedRecipeDetailView.swift */; };
		470B030CFE6F3D8EF8AF9C97 /* App.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C6BAB000A706022E5DE4311 /* App.swift */; };
		4CAC1DE461273018EEC02DA7 /* ResultsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B02FFC8EB3DFEB02AD57E0F2 /* ResultsViewModel.swift */; };
		4CADE21C3557740DC5825721 /* QuickResultsViewSnapshotTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18FFE68D22ADDE72EF29C790 /* QuickResultsViewSnapshotTests.swift */; };
		4DDFE5D0B3F7B40FEFBA78BD /* PantryStateProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDAD84D6504FD42EA06C4AFD /* PantryStateProvider.swift */; };
		4FD023AD5DA226B136291273 /* ToastView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2320D0A065905E7850A024EC /* ToastView.swift */; };
		4FF6F4EDD06DBBE415C5A6BA /* DoNotEatViewTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A15779E849FB63339A45C31 /* DoNotEatViewTests.swift */; };
		546FD3797670C44238850D6F /* ExpirationAlertManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* ExpirationAlertManager.swift */; };
		56D28E25E361C9BED25A9AE7 /* EnhancedRecipeCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2C5D3C601DCB6D96733C2AF3 /* EnhancedRecipeCard.swift */; };
		57C0F9F47D4919B96AB3C9A9 /* FavoritesStore.swift in Sources */ = {isa = PBXBuildFile; fileRef = DBF14D5276C786D79A3296EA /* FavoritesStore.swift */; };
		590EF66376A8348CBBBF4BCF /* GeminiAPIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 96E57515DB53E2C4895273E7 /* GeminiAPIService.swift */; };
		5959F28A176F961F1CB92521 /* BatchOperationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 738E39EDA2C3A648CDA26AC0 /* BatchOperationManager.swift */; };
		5A9EB1ABF0F85640272D1540 /* MealCutoffManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F7315F027BBD2E523243228C /* MealCutoffManager.swift */; };
		5BCCAB17E61CB7A78A253BAE /* PantryStateProviderTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 485125C0A942C587A2B3F368 /* PantryStateProviderTests.swift */; };
		5C0A0B8D64D2DE136D8CBE73 /* DietaryRestrictionsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E234451DACF3A41DDE0F90B6 /* DietaryRestrictionsView.swift */; };
		5CD42B751983ECB910C2409B /* RemoteConfigurationManagerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE7313BDD6245F5968889E7A /* RemoteConfigurationManagerTests.swift */; };
		5CD83495B880B4C371C615CB /* FamilyInfoManualTest.md in Resources */ = {isa = PBXBuildFile; fileRef = 7062A66F7F17C2328DC720D8 /* FamilyInfoManualTest.md */; };
		5CDD243A8D50CF3B1D79508C /* PreferencesEditView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B1D16C4399D928ED75C0D406 /* PreferencesEditView.swift */; };
		5ED14FC0EC3F3AA2E63FD535 /* APIKeys.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6E814F10D859D0594F1E3C2 /* APIKeys.swift */; };
		5FA62A0FB480B7DD1ACFFBFC /* ProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634B5D2304CD06FF2A54A345 /* ProfileView.swift */; };
		6124DD16BC643689DA54E870 /* ConfigurationSummaryTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DCA11F0EFCAF5EEF83855C7 /* ConfigurationSummaryTests.swift */; };
		6208AEA3C909440C0BBFAEA3 /* AuthenticationServiceTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2A65866BDC4D464B9AA9889 /* AuthenticationServiceTests.swift */; };
		62B466E9316457EDA57DBC06 /* PlanStoreMergePolicyTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA3EE027C1359C67EA6838E7 /* PlanStoreMergePolicyTests.swift */; };
		65BA3ABDC4AA873F2FBCD75B /* DiskStorageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8601375ED9DDE972E08AC0FB /* DiskStorageManager.swift */; };
		685D6F2A91A1502EAD217297 /* WeekTransitionAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8F4864EDFD1DC93E9C72FC82 /* WeekTransitionAnimator.swift */; };
		691855AD7EB52D27B499BC9D /* FavoritesEmptyStateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 356EEF6CAF37F5EDED9A0B5E /* FavoritesEmptyStateView.swift */; };
		6AF505AB5588F7B5F05F763E /* DoNotEatMigrationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = FF3330A24B7623BDBE3917DC /* DoNotEatMigrationTests.swift */; };
		6C6A417E7F5E0972303323D6 /* DebugViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 64498B16CC20397CD1568211 /* DebugViewModel.swift */; };
		6D0DB81AB702D79AA5385267 /* RecipeRequestBuilderTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE62E7F68F1DE3B693DC7B0F /* RecipeRequestBuilderTests.swift */; };
		6D79A5C099F8A11F3622CE42 /* ChangePasswordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* ChangePasswordView.swift */; };
		7101622282493DC519FE8A2B /* RecipeServiceAdapterTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD8E19A861A07FB044735E08 /* RecipeServiceAdapterTests.swift */; };
		717C81031AC06D5ECFDF445C /* HorizontalQuickResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3F4007B12B610265FCE59E7F /* HorizontalQuickResultsView.swift */; };
		728037D12D75D4B900849FE8 /* AsyncImagePreparationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2AE13D94EDA6D7C6CF36F861 /* AsyncImagePreparationService.swift */; };
		728D2FC9A70A90505EFEA9B2 /* DoNotEatSelectionStore.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0844A23144D1A11195522AED /* DoNotEatSelectionStore.swift */; };
		73076B2B7A47E522AAECBD7F /* RemoteConfigurationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = EACA497F9E6F77C9E4D26016 /* RemoteConfigurationManager.swift */; };
		73567D2A64329FD092A07A09 /* CalendarExpansionAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = C0C75B8D2C80F9A0FBA90BE1 /* CalendarExpansionAnimator.swift */; };
		73ECF5365DD896E0F048344C /* AppRouteTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 019EC1659BB4930E5F25FB68 /* AppRouteTests.swift */; };
		74F5ADC7DEBC005347DFF87A /* SwiftDataStorageService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81535A450F231A7A47014D5C /* SwiftDataStorageService.swift */; };
		759EE056788D582FC3FE6155 /* MealPlanCalendarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD0EE1A287D3518D7678381 /* MealPlanCalendarView.swift */; };
		76672B856A0F24B02726596D /* SummaryGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 508254809ABE98C8FD2CD764 /* SummaryGenerator.swift */; };
		78C944EA1DE275CA98A3A83A /* AllergiesIntolerancesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 14C94D6C263677D89C9261EF /* AllergiesIntolerancesView.swift */; };
		79E9C04FF0513FA36BBB5FCF /* PageIndicatorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C7E604EC9950F933DD22D95A /* PageIndicatorView.swift */; };
		7A3011B5F41A0480B47E8D1C /* StagingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6D8597742EEEA039E4F43ED6 /* StagingView.swift */; };
		7B02845B576A3CBC3ECA0C16 /* AuthenticationTestConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = DB5DFDC40745AC5FE1F38B7D /* AuthenticationTestConfig.swift */; };
		7D9460420F538FFBCF21FFDD /* MealSlotRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1923DDF532C40ABB691EE4F4 /* MealSlotRequest.swift */; };
		7FE8F41672ECA0D3AB9FD062 /* UserPreferencesCustomExclusionsTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A970C455DFCCEF754916B85 /* UserPreferencesCustomExclusionsTests.swift */; };
		804B6C0A9D4933B2F8818113 /* FirebaseAuth in Frameworks */ = {isa = PBXBuildFile; productRef = ABE43B4277A5DC9DFB4721A7 /* FirebaseAuth */; };
		82CFCA724ABD5520D70A210B /* GenerateButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 49FCA1EACFEE3F7BA83C1151 /* GenerateButton.swift */; };
		83EC4916030D1DE92209B3FB /* FamilySizeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F0CA4798A814D79944DB5ADA /* FamilySizeView.swift */; };
		84995C5F1A57FF917F21854C /* PreparedImage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9C4BB6A173DBA2A26848EBEB /* PreparedImage.swift */; };
		849D023AE6ECC7FC7E614880 /* AdditionalRequestEditor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 897C9D4CC68AA9A30767B1C0 /* AdditionalRequestEditor.swift */; };
		8501C5B848616AB2290EF198 /* CalendarMatrixView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74F9E581A20F80EA4479D452 /* CalendarMatrixView.swift */; };
		88D2F764123B52301A3B70B8 /* AppCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7CAF8BF954BEC15E0AA2C354 /* AppCoordinator.swift */; };
		89AFB66F259BE7565A5D1801 /* DebugView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79D1F4365F0010CABC1CADF7 /* DebugView.swift */; };
		89E02C1AE29DDB53BA692610 /* UpdateEmailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2E7130D09787BD13C10ABAE1 /* UpdateEmailView.swift */; };
		8C6699499ED137FE3BBEDE00 /* StagingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B01392334341FB8E206C523 /* StagingViewModel.swift */; };
		8E3182790C46308A760BF69D /* RecipeRequestBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = FE2E2AED4CE768FFB8719125 /* RecipeRequestBuilder.swift */; };
		8FFBEFCF2D0B8B70C7A25204 /* PlanSaveBannerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31E91787BB85372FF1F5C5E1 /* PlanSaveBannerView.swift */; };
		9175FB057F6581A929B418B9 /* QuickResultHistory.swift in Sources */ = {isa = PBXBuildFile; fileRef = ED1AFD29BA8ECAFC17F3BBAE /* QuickResultHistory.swift */; };
		929C5362403990D168CD15BC /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 8AE611CBEB6F9C65CA8F9BF6 /* GoogleService-Info.plist */; };
		93B3D23D7EC43832779112E6 /* FeedbackBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9AD0A4EBB4C3D398D77A4D04 /* FeedbackBar.swift */; };
		9634C6A0A790DFCF8DB74308 /* PantryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E09DF5AB9B43F2B5940B387E /* PantryView.swift */; };
		9980C88FF2EF298D70231142 /* ModeSelector.swift in Sources */ = {isa = PBXBuildFile; fileRef = 601D5438388977111C07153B /* ModeSelector.swift */; };
		9AAB43E55D0D950A9774FB0D /* RecipeCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B388B3CA95BA26238131006 /* RecipeCardView.swift */; };
		9AC0D19AA92FEA4636B57E8D /* Ingredient.swift in Sources */ = {isa = PBXBuildFile; fileRef = C354DB04ECE65CBF0CAE1FF5 /* Ingredient.swift */; };
		9AF8C2E290B054E66535276A /* CrossDeviceSyncTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD55B0B3D10F62784561A193 /* CrossDeviceSyncTests.swift */; };
		9B305BF7C2A046178293D6B1 /* MealCutoffManagerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = B7C7801EFEC4593FB2232DE5 /* MealCutoffManagerTests.swift */; };
		9B8AA968C7F1DBC83CB101DD /* StrictExclusionsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B900EC49B4467C5908ADB0E /* StrictExclusionsView.swift */; };
		9D480EEED0BF61466A814CC5 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B23ACFF89F562B95D79832BE /* SettingsView.swift */; };
		9D939EC81F1DF782C86A5704 /* MealConfigEditor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8AA0B415236EF9F69BB30E35 /* MealConfigEditor.swift */; };
		9EEE4E086180E916D0C8124F /* StructuredMealPlanGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7AFCDFB98CA5D00C81D0A8D8 /* StructuredMealPlanGenerator.swift */; };
		9F593E6A75E3601FB6CA8770 /* FirestoreError.swift in Sources */ = {isa = PBXBuildFile; fileRef = C0BF0FCC9FBAD1F55D36784E /* FirestoreError.swift */; };
		A13ABB8AE497C0CF26649E19 /* ConfigurationSummaryCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A503727A85F13B70F4996A5 /* ConfigurationSummaryCard.swift */; };
		A8EE005EC0421B741022FAE5 /* RecipeCompatibilityChips.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E3CD0B9F5B6D26D7A556D78 /* RecipeCompatibilityChips.swift */; };
		A9EE505F6BA66751830BC279 /* UserPreferences.swift in Sources */ = {isa = PBXBuildFile; fileRef = AB4EE67748A5F44F137361C2 /* UserPreferences.swift */; };
		AC5A62C1B1C0B0F554DC12F6 /* RecipeGenerationTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* RecipeGenerationTypes.swift */; };
		AEE1492DE07FCE6AEB73AC6A /* EquipmentChipsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C2B6872E34F7CB8A70082455 /* EquipmentChipsView.swift */; };
		AF1C7E872F143AB22E28AFEB /* DisplayError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 15BA12893C06D41D7F40712F /* DisplayError.swift */; };
		************************ /* PlansRetentionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9889DCAAFF16F7D88D2E2100 /* PlansRetentionManager.swift */; };
		B1049F793832B7463DCDED70 /* MealSelector.swift in Sources */ = {isa = PBXBuildFile; fileRef = EA863EB201AE20151A867CAD /* MealSelector.swift */; };
		B37EE88C3B635CDF91F452AA /* EditProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D5D253BFFAEBCDFBB530B58D /* EditProfileView.swift */; };
		B3D4185BDA01F72DC0980B28 /* ViewStateTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 29E9A068D2D1CEC81FD1B865 /* ViewStateTests.swift */; };
		B4C42ED1628DA6117B118113 /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = C7CA2B5F33DE3455FFB8FD33 /* GoogleSignIn */; };
		BB8716DF559A38032C1206B2 /* MealPlanGenerationRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C68A9E936A7EAA7E7E43E81 /* MealPlanGenerationRequest.swift */; };
		BD3AE20BD7773B82A26D4290 /* CanGenerateLogicTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 66A68E1CF81B5597FEA5A6D6 /* CanGenerateLogicTests.swift */; };
		BDBFE1EE64AC1ACE45B2E9E8 /* api-keys.plist in Resources */ = {isa = PBXBuildFile; fileRef = 9BF94B7ADBCEE860F7B723CC /* api-keys.plist */; };
		BE61FB414F25F66B2C608709 /* DoNotEatView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B76F53F1BD3659DECA387F2B /* DoNotEatView.swift */; };
		BE82E83A77935AA118FD1AA6 /* TelemetryEventBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4BF734808AFF0C664EA566A1 /* TelemetryEventBuilder.swift */; };
		BF4FB6EDD3523DB2EA76C06B /* OptimizedNetworkService.swift in Sources */ = {isa = PBXBuildFile; fileRef = C232D41B35C988DB1ED2C337 /* OptimizedNetworkService.swift */; };
		C0F0F6890E1A023460BE88CA /* ManageAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = D53F32D3F9209C84D51E3BB6 /* ManageAction.swift */; };
		C1A180C6BD50C3FA1A4D4F74 /* AccountTelemetryHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* AccountTelemetryHelper.swift */; };
		C7D500DD1FECD01DC8676CBF /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = 712679BBC79E5B5AA28585E5 /* README.md */; };
		C93D1E772CC97F0DFA65CD27 /* Haptics.swift in Sources */ = {isa = PBXBuildFile; fileRef = F051D9F4E367F48AC03E0B61 /* Haptics.swift */; };
		CC1C55DA7952A3D044AC8245 /* PlanStore.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F8D4B506DEF312B02334F4C /* PlanStore.swift */; };
		CFFD729D8B69158EC7D15475 /* FavoritesManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3DDEF4EBC4D5CA9A959AFA91 /* FavoritesManager.swift */; };
		D2694E9BF7B54C29F8F913BE /* RegenerateModeState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 55ACDE8516C85BC6B4D16CC7 /* RegenerateModeState.swift */; };
		D2CB3623DB80D90596DFAFB8 /* RecipeCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1F1322C3C8EB8275E9862E22 /* RecipeCard.swift */; };
		D6212D7B0BB73FD279EB95E2 /* PantryDeletionTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6422EEE3D50DD3ABDC943DAC /* PantryDeletionTests.swift */; };
		D79545A53384C3A69227721F /* UserProfileService.swift in Sources */ = {isa = PBXBuildFile; fileRef = E45162584291B28B845255C8 /* UserProfileService.swift */; };
		D848379A5A753AB625F95407 /* CacheKeyGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6F286483F9B3B05ECF83C179 /* CacheKeyGenerator.swift */; };
		D86249C65F27BEE3D39041E4 /* RecipeDetail.swift in Sources */ = {isa = PBXBuildFile; fileRef = BFD881A4E73B5A047F08F6EB /* RecipeDetail.swift */; };
		DC6AC64160026E6BE9CA4292 /* ProcessingScreen.swift in Sources */ = {isa = PBXBuildFile; fileRef = D561CE9DD4BF726A0517475C /* ProcessingScreen.swift */; };
		DCD621CA55AD07654E60141C /* CapacityIndicatorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3D132F616063633C3CF22748 /* CapacityIndicatorView.swift */; };
		DD9941CC6DE72C5723EE443C /* MealPlanDateRangeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0A075453B11D3A164A272C03 /* MealPlanDateRangeTests.swift */; };
		DEFA83DECCA0F8397BBA36DA /* RecipeServiceAdapter.swift in Sources */ = {isa = PBXBuildFile; fileRef = D76E654E89C20F29237B9984 /* RecipeServiceAdapter.swift */; };
		DFFDB75459955E361F07758E /* MealSlotIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = F75A5AB95205838619B79FEE /* MealSlotIndicator.swift */; };
		E1221CAAC6402BD0D825E9EC /* WeekIndicatorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7B74D0BA67827234B1C6D87F /* WeekIndicatorView.swift */; };
		E349857AFEDCC9D013E68A7D /* PlansEmptyStateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D10DA713D4A39B43DBEA9C7E /* PlansEmptyStateView.swift */; };
		E3BCB02F70895E97E5DFDD42 /* PlansHistoryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C2AD1028F7AB7D4ED582D627 /* PlansHistoryView.swift */; };
		E684CC98841467E1D92339FE /* GoogleVisionAPIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* GoogleVisionAPIService.swift */; };
		E6AFDE8843CE77D77D18BF41 /* DayPlan.swift in Sources */ = {isa = PBXBuildFile; fileRef = 40BCDE3BE7D8C1EE496251EE /* DayPlan.swift */; };
		E6D16E11F54A7B3AC607A39C /* RecipeGeneratorViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF6E326DF3C70BF39313BBD0 /* RecipeGeneratorViewModel.swift */; };
		E8575D4C691F3DA906C99D8E /* QuickResultPageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4476EDC80C21FDEA1F3988E6 /* QuickResultPageView.swift */; };
		E9AE0994D018F35608020BE9 /* ProfileAccountView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B64D61BE29009F0227EA30F5 /* ProfileAccountView.swift */; };
		************************ /* MealSlot.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9B923A799337A751F8CA037 /* MealSlot.swift */; };
		EA8F89BE23C5724048B3613B /* QuickHistoryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBC17A041025DFCE982EE44F /* QuickHistoryView.swift */; };
		EC9C0184D659F23C2A4D1112 /* RecipeGenerationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* RecipeGenerationService.swift */; };
		EDB1FCBE871A2FC42E1BB1B0 /* RecipeGeneratorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6F89EB82B149F68E8B504FA /* RecipeGeneratorView.swift */; };
		EEED6D2A397A1A7701CA5D9C /* RecipeGenerationRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2D0D2194CA4CE570714310BB /* RecipeGenerationRequest.swift */; };
		EFEA08C3450FB79F8941B2F8 /* NotificationsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* NotificationsView.swift */; };
		F0CB0A2302140E99F85EF1E2 /* StorageConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 633E17F5B1C449AB300F5F0F /* StorageConfiguration.swift */; };
		F0D28AA28203A45724F3BF5A /* FavoriteButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9C4917480BCB350A2F6FF644 /* FavoriteButton.swift */; };
		F0EE2FFD7ABAD7F6A56C5E87 /* PerformanceMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8F0DEB57B33E9A9367B2B922 /* PerformanceMonitor.swift */; };
		F34EAA77130AA93558A20E26 /* IngredientLibrary.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6C33700D0F38BFB26D4F150 /* IngredientLibrary.swift */; };
		F43375FB4DB343221C759399 /* RecipeGeneratorTimeoutTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A27432683AE4E32656D9D190 /* RecipeGeneratorTimeoutTests.swift */; };
		F55F90C52C302559DC2738DA /* FoodPreferencesRegressionTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE644A19074040E7887A430A /* FoodPreferencesRegressionTests.swift */; };
		F56C53600070BF9D777511E9 /* HistoryStorageService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74C9832A91945C87A0A70C4F /* HistoryStorageService.swift */; };
		F5CE6AFD5A60E98F7FA9586E /* APIProtocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 59F5CB5CD990DF3795FBEA8D /* APIProtocols.swift */; };
		F97E1CE239E7C4724DABF792 /* RecipesViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5DDDD7E86CC44475BCCD2D93 /* RecipesViewModel.swift */; };
		F9A41FBACF9CCA4A42169055 /* ServiceContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CA085D99E48956579051C8A /* ServiceContainer.swift */; };
		FDE88250E2BBE576907DC109 /* ReauthenticationCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CF52E4781421CC9B5270212 /* ReauthenticationCoordinator.swift */; };
		FF18CF88C55BAAED3384A5A3 /* TelemetryService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 67762E49563B93104A5F83EC /* TelemetryService.swift */; };
		FF928F3C52B45DF86B4B9A4E /* FavoritesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F41F0DE8D133CECCA075986E /* FavoritesView.swift */; };
		FFAE4C707F09293C4B89603B /* AuthenticationIntegrationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 291A9FD30D4F2B1F7BD287C9 /* AuthenticationIntegrationTests.swift */; };
		FFDEDB4FB90539DCE232F105 /* MemoryManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D15F4CF782D3C898AF63928 /* MemoryManager.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		466BBAE823935D9CC313E6A7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2212BE8852EE267CB6C8FE89 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 930782F8BD78C73099163318;
			remoteInfo = IngredientScanner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		002990977B33054F36A3499D /* AuthenticationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationService.swift; sourceTree = "<group>"; };
		00C6D31A5B45C429D2C7BC73 /* RecipeUIModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeUIModel.swift; sourceTree = "<group>"; };
		019EC1659BB4930E5F25FB68 /* AppRouteTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppRouteTests.swift; sourceTree = "<group>"; };
		0844A23144D1A11195522AED /* DoNotEatSelectionStore.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DoNotEatSelectionStore.swift; sourceTree = "<group>"; };
		0A075453B11D3A164A272C03 /* MealPlanDateRangeTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MealPlanDateRangeTests.swift; sourceTree = "<group>"; };
		0B01392334341FB8E206C523 /* StagingViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StagingViewModel.swift; sourceTree = "<group>"; };
		0B1553BA82F6EB30E6E2C6CB /* AsyncTimeoutTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AsyncTimeoutTests.swift; sourceTree = "<group>"; };
		0B900EC49B4467C5908ADB0E /* StrictExclusionsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrictExclusionsView.swift; sourceTree = "<group>"; };
		119110C234FA4B3A7A158FBC /* CustomItemEntrySheet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomItemEntrySheet.swift; sourceTree = "<group>"; };
		11DBEC63A8F70F25F4C5480C /* MultiImagePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultiImagePicker.swift; sourceTree = "<group>"; };
		14C94D6C263677D89C9261EF /* AllergiesIntolerancesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AllergiesIntolerancesView.swift; sourceTree = "<group>"; };
		15BA12893C06D41D7F40712F /* DisplayError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DisplayError.swift; sourceTree = "<group>"; };
		1713B0A4BB0475F6E1513321 /* SwipeableMealPlanCalendarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwipeableMealPlanCalendarView.swift; sourceTree = "<group>"; };
		176398FEADBD4CF189075941 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		18FFE68D22ADDE72EF29C790 /* QuickResultsViewSnapshotTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickResultsViewSnapshotTests.swift; sourceTree = "<group>"; };
		1923DDF532C40ABB691EE4F4 /* MealSlotRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MealSlotRequest.swift; sourceTree = "<group>"; };
		1A503727A85F13B70F4996A5 /* ConfigurationSummaryCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigurationSummaryCard.swift; sourceTree = "<group>"; };
		1F1322C3C8EB8275E9862E22 /* RecipeCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeCard.swift; sourceTree = "<group>"; };
		1F608E2C70FC1D3D0EFE40EA /* QuickHistoryIndexEntry.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickHistoryIndexEntry.swift; sourceTree = "<group>"; };
		1FAE924D1A86CB90B9B39DE0 /* AccountResetManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccountResetManager.swift; sourceTree = "<group>"; };
		203CBC78A5DD35F560757709 /* QuickResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickResultsView.swift; sourceTree = "<group>"; };
		2320D0A065905E7850A024EC /* ToastView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ToastView.swift; sourceTree = "<group>"; };
		254B55743876E6E92DCC2F22 /* ReauthenticationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReauthenticationView.swift; sourceTree = "<group>"; };
		2584E995A79858BB53E06153 /* GeneratedRecipeDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeneratedRecipeDetailView.swift; sourceTree = "<group>"; };
		269ABED48A4B361CEFD34F9B /* MealPlan.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MealPlan.swift; sourceTree = "<group>"; };
		291A9FD30D4F2B1F7BD287C9 /* AuthenticationIntegrationTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationIntegrationTests.swift; sourceTree = "<group>"; };
		29E9A068D2D1CEC81FD1B865 /* ViewStateTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewStateTests.swift; sourceTree = "<group>"; };
		2AE13D94EDA6D7C6CF36F861 /* AsyncImagePreparationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AsyncImagePreparationService.swift; sourceTree = "<group>"; };
		2BD0EE1A287D3518D7678381 /* MealPlanCalendarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MealPlanCalendarView.swift; sourceTree = "<group>"; };
		2C5D3C601DCB6D96733C2AF3 /* EnhancedRecipeCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnhancedRecipeCard.swift; sourceTree = "<group>"; };
		2D0D2194CA4CE570714310BB /* RecipeGenerationRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGenerationRequest.swift; sourceTree = "<group>"; };
		2E7130D09787BD13C10ABAE1 /* UpdateEmailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpdateEmailView.swift; sourceTree = "<group>"; };
		2F8D4B506DEF312B02334F4C /* PlanStore.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlanStore.swift; sourceTree = "<group>"; };
		31E91787BB85372FF1F5C5E1 /* PlanSaveBannerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlanSaveBannerView.swift; sourceTree = "<group>"; };
		356EEF6CAF37F5EDED9A0B5E /* FavoritesEmptyStateView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoritesEmptyStateView.swift; sourceTree = "<group>"; };
		35CDF50BB12C724D6BBE8CC2 /* ViewState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewState.swift; sourceTree = "<group>"; };
		36DC5C478C9FA9ECC7F0D47F /* DoNotEatHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DoNotEatHelper.swift; sourceTree = "<group>"; };
		38C4E523471A626055C6E95E /* CollapsedCalendarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CollapsedCalendarView.swift; sourceTree = "<group>"; };
		3C4267156C55BCA053A6D391 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		3C6BAB000A706022E5DE4311 /* App.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = App.swift; sourceTree = "<group>"; };
		3C7CD7EB90744FA9A3D485D6 /* CuisinesChips.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CuisinesChips.swift; sourceTree = "<group>"; };
		3CA085D99E48956579051C8A /* ServiceContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ServiceContainer.swift; sourceTree = "<group>"; };
		3D132F616063633C3CF22748 /* CapacityIndicatorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CapacityIndicatorView.swift; sourceTree = "<group>"; };
		3DDEF4EBC4D5CA9A959AFA91 /* FavoritesManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoritesManager.swift; sourceTree = "<group>"; };
		3F4007B12B610265FCE59E7F /* HorizontalQuickResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HorizontalQuickResultsView.swift; sourceTree = "<group>"; };
		40BCDE3BE7D8C1EE496251EE /* DayPlan.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DayPlan.swift; sourceTree = "<group>"; };
		416FE6AD741760160A09D36A /* RegenerateRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegenerateRequest.swift; sourceTree = "<group>"; };
		4418C6EBABFCD397C6708DB9 /* RecipeHistoryTabView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeHistoryTabView.swift; sourceTree = "<group>"; };
		4476EDC80C21FDEA1F3988E6 /* QuickResultPageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickResultPageView.swift; sourceTree = "<group>"; };
		44C234B0CF86ED71B89397C0 /* LoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingView.swift; sourceTree = "<group>"; };
		485125C0A942C587A2B3F368 /* PantryStateProviderTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryStateProviderTests.swift; sourceTree = "<group>"; };
		49FCA1EACFEE3F7BA83C1151 /* GenerateButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GenerateButton.swift; sourceTree = "<group>"; };
		4BF734808AFF0C664EA566A1 /* TelemetryEventBuilder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TelemetryEventBuilder.swift; sourceTree = "<group>"; };
		4EAA0C9279CD7A5A60C41D4C /* TelemetryFeedbackTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TelemetryFeedbackTests.swift; sourceTree = "<group>"; };
		5034CD8F3B40B2B21EB5A035 /* NameCanonicalizer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NameCanonicalizer.swift; sourceTree = "<group>"; };
		508254809ABE98C8FD2CD764 /* SummaryGenerator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SummaryGenerator.swift; sourceTree = "<group>"; };
		51F07E8F5B2F774E45C5AD58 /* GroupedResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GroupedResultsView.swift; sourceTree = "<group>"; };
		55ACDE8516C85BC6B4D16CC7 /* RegenerateModeState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegenerateModeState.swift; sourceTree = "<group>"; };
		561B59FB1C991C51A3AF05DB /* RecipeGrouper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGrouper.swift; sourceTree = "<group>"; };
		58F7E951E15E903C3F2DA88E /* ResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResultsView.swift; sourceTree = "<group>"; };
		59F5CB5CD990DF3795FBEA8D /* APIProtocols.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIProtocols.swift; sourceTree = "<group>"; };
		5DDDD7E86CC44475BCCD2D93 /* RecipesViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipesViewModel.swift; sourceTree = "<group>"; };
		601D5438388977111C07153B /* ModeSelector.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModeSelector.swift; sourceTree = "<group>"; };
		618BC01DC46E43C192A972EA /* ConcurrencyManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConcurrencyManager.swift; sourceTree = "<group>"; };
		633E17F5B1C449AB300F5F0F /* StorageConfiguration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StorageConfiguration.swift; sourceTree = "<group>"; };
		634B5D2304CD06FF2A54A345 /* ProfileView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileView.swift; sourceTree = "<group>"; };
		6422EEE3D50DD3ABDC943DAC /* PantryDeletionTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryDeletionTests.swift; sourceTree = "<group>"; };
		64498B16CC20397CD1568211 /* DebugViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DebugViewModel.swift; sourceTree = "<group>"; };
		65151B8943DDA722BFA8A88F /* QuickResultCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickResultCardView.swift; sourceTree = "<group>"; };
		65C9D20B21C9792C0477E9CB /* ErrorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ErrorView.swift; sourceTree = "<group>"; };
		66A68E1CF81B5597FEA5A6D6 /* CanGenerateLogicTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CanGenerateLogicTests.swift; sourceTree = "<group>"; };
		67762E49563B93104A5F83EC /* TelemetryService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TelemetryService.swift; sourceTree = "<group>"; };
		6A2A3C01702A9D29F93ECE9D /* MealPlanDateRange.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MealPlanDateRange.swift; sourceTree = "<group>"; };
		6CF52E4781421CC9B5270212 /* ReauthenticationCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReauthenticationCoordinator.swift; sourceTree = "<group>"; };
		6D8597742EEEA039E4F43ED6 /* StagingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StagingView.swift; sourceTree = "<group>"; };
		6F286483F9B3B05ECF83C179 /* CacheKeyGenerator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CacheKeyGenerator.swift; sourceTree = "<group>"; };
		7062A66F7F17C2328DC720D8 /* FamilyInfoManualTest.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = FamilyInfoManualTest.md; sourceTree = "<group>"; };
		70D683F0BF9B783BB0F51EE6 /* IngredientScanner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = IngredientScanner.entitlements; sourceTree = "<group>"; };
		712679BBC79E5B5AA28585E5 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		72BDAC489562E49EF25728FB /* WeeklyMealPlan.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WeeklyMealPlan.swift; sourceTree = "<group>"; };
		738E39EDA2C3A648CDA26AC0 /* BatchOperationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BatchOperationManager.swift; sourceTree = "<group>"; };
		74A0B7153B64EF215DE57ABB /* GenerationFingerprint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GenerationFingerprint.swift; sourceTree = "<group>"; };
		74C9832A91945C87A0A70C4F /* HistoryStorageService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryStorageService.swift; sourceTree = "<group>"; };
		74F9E581A20F80EA4479D452 /* CalendarMatrixView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarMatrixView.swift; sourceTree = "<group>"; };
		79D1F4365F0010CABC1CADF7 /* DebugView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DebugView.swift; sourceTree = "<group>"; };
		7A3DBA7A6E753996362EB552 /* QuickHistoryManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickHistoryManager.swift; sourceTree = "<group>"; };
		7A884A291FA4749F093A2A99 /* DoNotEatHelperTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DoNotEatHelperTests.swift; sourceTree = "<group>"; };
		7AFCDFB98CA5D00C81D0A8D8 /* StructuredMealPlanGenerator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StructuredMealPlanGenerator.swift; sourceTree = "<group>"; };
		7B74D0BA67827234B1C6D87F /* WeekIndicatorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WeekIndicatorView.swift; sourceTree = "<group>"; };
		7BC9FDEE48F6CEFBC05DBDB3 /* FavoriteItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoriteItem.swift; sourceTree = "<group>"; };
		7CAF8BF954BEC15E0AA2C354 /* AppCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppCoordinator.swift; sourceTree = "<group>"; };
		7D15F4CF782D3C898AF63928 /* MemoryManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MemoryManager.swift; sourceTree = "<group>"; };
		7E3CD0B9F5B6D26D7A556D78 /* RecipeCompatibilityChips.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeCompatibilityChips.swift; sourceTree = "<group>"; };
		81535A450F231A7A47014D5C /* SwiftDataStorageService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftDataStorageService.swift; sourceTree = "<group>"; };
		84B3CC06B1FB6E25E979F9D9 /* PantryCategory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryCategory.swift; sourceTree = "<group>"; };
		8601375ED9DDE972E08AC0FB /* DiskStorageManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DiskStorageManager.swift; sourceTree = "<group>"; };
		897C9D4CC68AA9A30767B1C0 /* AdditionalRequestEditor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdditionalRequestEditor.swift; sourceTree = "<group>"; };
		8A15779E849FB63339A45C31 /* DoNotEatViewTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DoNotEatViewTests.swift; sourceTree = "<group>"; };
		8A970C455DFCCEF754916B85 /* UserPreferencesCustomExclusionsTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserPreferencesCustomExclusionsTests.swift; sourceTree = "<group>"; };
		8AA0B415236EF9F69BB30E35 /* MealConfigEditor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MealConfigEditor.swift; sourceTree = "<group>"; };
		8AE611CBEB6F9C65CA8F9BF6 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		8B0B1D1EECDE31EBE316148E /* ExpirationNotificationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExpirationNotificationManager.swift; sourceTree = "<group>"; };
		8C68A9E936A7EAA7E7E43E81 /* MealPlanGenerationRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MealPlanGenerationRequest.swift; sourceTree = "<group>"; };
		8DCA11F0EFCAF5EEF83855C7 /* ConfigurationSummaryTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigurationSummaryTests.swift; sourceTree = "<group>"; };
		8F0DEB57B33E9A9367B2B922 /* PerformanceMonitor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PerformanceMonitor.swift; sourceTree = "<group>"; };
		8F4864EDFD1DC93E9C72FC82 /* WeekTransitionAnimator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WeekTransitionAnimator.swift; sourceTree = "<group>"; };
		96E57515DB53E2C4895273E7 /* GeminiAPIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeminiAPIService.swift; sourceTree = "<group>"; };
		9889DCAAFF16F7D88D2E2100 /* PlansRetentionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlansRetentionManager.swift; sourceTree = "<group>"; };
		98B8CC92CB9B5D9E94B23193 /* RecipesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipesView.swift; sourceTree = "<group>"; };
		9A4AB7496EBE832DB8795330 /* ExpandedCalendarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExpandedCalendarView.swift; sourceTree = "<group>"; };
		9AD0A4EBB4C3D398D77A4D04 /* FeedbackBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeedbackBar.swift; sourceTree = "<group>"; };
		9B388B3CA95BA26238131006 /* RecipeCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeCardView.swift; sourceTree = "<group>"; };
		9BBD1837E2145AFF47B7839D /* IngredientScannerTests.xctest */ = {isa = PBXFileReference; includeInIndex = 0; lastKnownFileType = wrapper.cfbundle; path = IngredientScannerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		9BF94B7ADBCEE860F7B723CC /* api-keys.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = "api-keys.plist"; sourceTree = "<group>"; };
		9C4917480BCB350A2F6FF644 /* FavoriteButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoriteButton.swift; sourceTree = "<group>"; };
		9C4BB6A173DBA2A26848EBEB /* PreparedImage.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreparedImage.swift; sourceTree = "<group>"; };
		9FD4633A895C41348C22C35D /* RemoteConfiguration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RemoteConfiguration.swift; sourceTree = "<group>"; };
		9FFE3881D55D069B1B730E46 /* SignInView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SignInView.swift; sourceTree = "<group>"; };
		A0AE66C6EA17D96111A50370 /* ConfigurationSummary.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigurationSummary.swift; sourceTree = "<group>"; };
		A27432683AE4E32656D9D190 /* RecipeGeneratorTimeoutTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGeneratorTimeoutTests.swift; sourceTree = "<group>"; };
		A6C33700D0F38BFB26D4F150 /* IngredientLibrary.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IngredientLibrary.swift; sourceTree = "<group>"; };
		A6E814F10D859D0594F1E3C2 /* APIKeys.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIKeys.swift; sourceTree = "<group>"; };
		A8BE75E54566F07167996995 /* DaysSelector.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DaysSelector.swift; sourceTree = "<group>"; };
		A8BF948EA96EBF61EA2C4D8E /* NavigationCoordinatorTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NavigationCoordinatorTests.swift; sourceTree = "<group>"; };
		A9B923A799337A751F8CA037 /* MealSlot.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MealSlot.swift; sourceTree = "<group>"; };
		AB4EE67748A5F44F137361C2 /* UserPreferences.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserPreferences.swift; sourceTree = "<group>"; };
		AE22904B43C38F5DA2C1BCCE /* Recipe.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Recipe.swift; sourceTree = "<group>"; };
		B02FFC8EB3DFEB02AD57E0F2 /* ResultsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResultsViewModel.swift; sourceTree = "<group>"; };
		B1D16C4399D928ED75C0D406 /* PreferencesEditView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreferencesEditView.swift; sourceTree = "<group>"; };
		B23ACFF89F562B95D79832BE /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		B2A65866BDC4D464B9AA9889 /* AuthenticationServiceTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationServiceTests.swift; sourceTree = "<group>"; };
		B356A9042F89FA0965EF0447 /* RecipeCardHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeCardHeaderView.swift; sourceTree = "<group>"; };
		B64D61BE29009F0227EA30F5 /* ProfileAccountView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileAccountView.swift; sourceTree = "<group>"; };
		B76F53F1BD3659DECA387F2B /* DoNotEatView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DoNotEatView.swift; sourceTree = "<group>"; };
		B7C7801EFEC4593FB2232DE5 /* MealCutoffManagerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MealCutoffManagerTests.swift; sourceTree = "<group>"; };
		************************ /* ChangePasswordView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChangePasswordView.swift; sourceTree = "<group>"; };
		BAC67331A1B12A7A1522632C /* RetentionPolicy.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RetentionPolicy.swift; sourceTree = "<group>"; };
		************************ /* RecipeGenerationTypes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGenerationTypes.swift; sourceTree = "<group>"; };
		BDAD84D6504FD42EA06C4AFD /* PantryStateProvider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryStateProvider.swift; sourceTree = "<group>"; };
		BE62E7F68F1DE3B693DC7B0F /* RecipeRequestBuilderTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeRequestBuilderTests.swift; sourceTree = "<group>"; };
		BE644A19074040E7887A430A /* FoodPreferencesRegressionTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FoodPreferencesRegressionTests.swift; sourceTree = "<group>"; };
		BE7313BDD6245F5968889E7A /* RemoteConfigurationManagerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RemoteConfigurationManagerTests.swift; sourceTree = "<group>"; };
		BEEFC3C7A57736C5A6735DB9 /* RecipeGrouperTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGrouperTests.swift; sourceTree = "<group>"; };
		BFD881A4E73B5A047F08F6EB /* RecipeDetail.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeDetail.swift; sourceTree = "<group>"; };
		C093F1FD032970F0D124F474 /* ImagePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePicker.swift; sourceTree = "<group>"; };
		C0BF0FCC9FBAD1F55D36784E /* FirestoreError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FirestoreError.swift; sourceTree = "<group>"; };
		C0C75B8D2C80F9A0FBA90BE1 /* CalendarExpansionAnimator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarExpansionAnimator.swift; sourceTree = "<group>"; };
		C0F436AB8DE0B60A131EAA60 /* PantryStateWarning.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryStateWarning.swift; sourceTree = "<group>"; };
		C232D41B35C988DB1ED2C337 /* OptimizedNetworkService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OptimizedNetworkService.swift; sourceTree = "<group>"; };
		C2AD1028F7AB7D4ED582D627 /* PlansHistoryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlansHistoryView.swift; sourceTree = "<group>"; };
		C2B6872E34F7CB8A70082455 /* EquipmentChipsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EquipmentChipsView.swift; sourceTree = "<group>"; };
		C354DB04ECE65CBF0CAE1FF5 /* Ingredient.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Ingredient.swift; sourceTree = "<group>"; };
		C7E604EC9950F933DD22D95A /* PageIndicatorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PageIndicatorView.swift; sourceTree = "<group>"; };
		CBB03341FB7787DBE4C01335 /* EquipmentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EquipmentView.swift; sourceTree = "<group>"; };
		CBC17A041025DFCE982EE44F /* QuickHistoryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickHistoryView.swift; sourceTree = "<group>"; };
		CD8E19A861A07FB044735E08 /* RecipeServiceAdapterTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeServiceAdapterTests.swift; sourceTree = "<group>"; };
		CDDDEC1AA2F6B5C324E8D90D /* DoNotEatHelpSheet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DoNotEatHelpSheet.swift; sourceTree = "<group>"; };
		************************ /* RecipeGenerationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGenerationService.swift; sourceTree = "<group>"; };
		CF9EE15071AC0CEEA67DC8EA /* SignInManualTest.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = SignInManualTest.md; sourceTree = "<group>"; };
		D0E63A4B380015E8E3BF68C8 /* IngredientScanner.app */ = {isa = PBXFileReference; includeInIndex = 0; lastKnownFileType = wrapper.application; path = IngredientScanner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		D10DA713D4A39B43DBEA9C7E /* PlansEmptyStateView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlansEmptyStateView.swift; sourceTree = "<group>"; };
		D4867FFC3C03EA927A2A4619 /* SwiftDataModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftDataModels.swift; sourceTree = "<group>"; };
		D4E1A6DF07EA911BE766638A /* ManageSelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ManageSelectionView.swift; sourceTree = "<group>"; };
		D53F32D3F9209C84D51E3BB6 /* ManageAction.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ManageAction.swift; sourceTree = "<group>"; };
		D561CE9DD4BF726A0517475C /* ProcessingScreen.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProcessingScreen.swift; sourceTree = "<group>"; };
		D5D253BFFAEBCDFBB530B58D /* EditProfileView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditProfileView.swift; sourceTree = "<group>"; };
		D6F89EB82B149F68E8B504FA /* RecipeGeneratorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGeneratorView.swift; sourceTree = "<group>"; };
		D76E654E89C20F29237B9984 /* RecipeServiceAdapter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeServiceAdapter.swift; sourceTree = "<group>"; };
		D94EBA0AA79E38BB545F9F61 /* PantryViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryViewModel.swift; sourceTree = "<group>"; };
		************************ /* GoogleVisionAPIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoogleVisionAPIService.swift; sourceTree = "<group>"; };
		DB5DFDC40745AC5FE1F38B7D /* AuthenticationTestConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationTestConfig.swift; sourceTree = "<group>"; };
		DBF14D5276C786D79A3296EA /* FavoritesStore.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoritesStore.swift; sourceTree = "<group>"; };
		DD55B0B3D10F62784561A193 /* CrossDeviceSyncTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CrossDeviceSyncTests.swift; sourceTree = "<group>"; };
		DD5874C638A51E908AA3C197 /* RecipeDetailCache.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeDetailCache.swift; sourceTree = "<group>"; };
		DF6E326DF3C70BF39313BBD0 /* RecipeGeneratorViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGeneratorViewModel.swift; sourceTree = "<group>"; };
		DFC23B81C1836632A9C0C7D1 /* PantryService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryService.swift; sourceTree = "<group>"; };
		E09DF5AB9B43F2B5940B387E /* PantryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryView.swift; sourceTree = "<group>"; };
		E234451DACF3A41DDE0F90B6 /* DietaryRestrictionsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DietaryRestrictionsView.swift; sourceTree = "<group>"; };
		E45162584291B28B845255C8 /* UserProfileService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserProfileService.swift; sourceTree = "<group>"; };
		EA863EB201AE20151A867CAD /* MealSelector.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MealSelector.swift; sourceTree = "<group>"; };
		EACA497F9E6F77C9E4D26016 /* RemoteConfigurationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RemoteConfigurationManager.swift; sourceTree = "<group>"; };
		ED1AFD29BA8ECAFC17F3BBAE /* QuickResultHistory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickResultHistory.swift; sourceTree = "<group>"; };
		************************ /* AccountTelemetryHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccountTelemetryHelper.swift; sourceTree = "<group>"; };
		F051D9F4E367F48AC03E0B61 /* Haptics.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Haptics.swift; sourceTree = "<group>"; };
		F0CA4798A814D79944DB5ADA /* FamilySizeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FamilySizeView.swift; sourceTree = "<group>"; };
		F41F0DE8D133CECCA075986E /* FavoritesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoritesView.swift; sourceTree = "<group>"; };
		F5D207A9E7FF3219859AD0FC /* QuickEmptyStateView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickEmptyStateView.swift; sourceTree = "<group>"; };
		F7315F027BBD2E523243228C /* MealCutoffManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MealCutoffManager.swift; sourceTree = "<group>"; };
		F75A5AB95205838619B79FEE /* MealSlotIndicator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MealSlotIndicator.swift; sourceTree = "<group>"; };
		FA3EE027C1359C67EA6838E7 /* PlanStoreMergePolicyTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlanStoreMergePolicyTests.swift; sourceTree = "<group>"; };
		************************ /* ExpirationAlertManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExpirationAlertManager.swift; sourceTree = "<group>"; };
		************************ /* NotificationsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationsView.swift; sourceTree = "<group>"; };
		FE2E2AED4CE768FFB8719125 /* RecipeRequestBuilder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeRequestBuilder.swift; sourceTree = "<group>"; };
		FF3330A24B7623BDBE3917DC /* DoNotEatMigrationTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DoNotEatMigrationTests.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0786AF5BB94AD8A44FA2C5E8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				804B6C0A9D4933B2F8818113 /* FirebaseAuth in Frameworks */,
				0284549EC8FD2A757D22C0FB /* FirebaseFirestore in Frameworks */,
				07CA588ECA8D0CC719AEE696 /* FirebaseAnalytics in Frameworks */,
				B4C42ED1628DA6117B118113 /* GoogleSignIn in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1FC412429FB925EF376F80A6 /* 1_ImageCapture */ = {
			isa = PBXGroup;
			children = (
				6D8597742EEEA039E4F43ED6 /* StagingView.swift */,
				0B01392334341FB8E206C523 /* StagingViewModel.swift */,
			);
			path = 1_ImageCapture;
			sourceTree = "<group>";
		};
		3B995C1EF162DAD5B2491BF9 /* Profile */ = {
			isa = PBXGroup;
			children = (
				14C94D6C263677D89C9261EF /* AllergiesIntolerancesView.swift */,
				119110C234FA4B3A7A158FBC /* CustomItemEntrySheet.swift */,
				E234451DACF3A41DDE0F90B6 /* DietaryRestrictionsView.swift */,
				CDDDEC1AA2F6B5C324E8D90D /* DoNotEatHelpSheet.swift */,
				0844A23144D1A11195522AED /* DoNotEatSelectionStore.swift */,
				B76F53F1BD3659DECA387F2B /* DoNotEatView.swift */,
				C2B6872E34F7CB8A70082455 /* EquipmentChipsView.swift */,
				CBB03341FB7787DBE4C01335 /* EquipmentView.swift */,
				65C9D20B21C9792C0477E9CB /* ErrorView.swift */,
				F0CA4798A814D79944DB5ADA /* FamilySizeView.swift */,
				44C234B0CF86ED71B89397C0 /* LoadingView.swift */,
				************************ /* NotificationsView.swift */,
				B1D16C4399D928ED75C0D406 /* PreferencesEditView.swift */,
				634B5D2304CD06FF2A54A345 /* ProfileView.swift */,
				B23ACFF89F562B95D79832BE /* SettingsView.swift */,
				9FFE3881D55D069B1B730E46 /* SignInView.swift */,
				0B900EC49B4467C5908ADB0E /* StrictExclusionsView.swift */,
				2320D0A065905E7850A024EC /* ToastView.swift */,
				6073F23F3DD4088E2B182FC6 /* Account */,
				B4316D9D9DE845B78D7D90CD /* Reauthentication */,
			);
			path = Profile;
			sourceTree = "<group>";
		};
		3D0BCE87402CE9603601FF27 /* Utils */ = {
			isa = PBXGroup;
			children = (
				738E39EDA2C3A648CDA26AC0 /* BatchOperationManager.swift */,
				6F286483F9B3B05ECF83C179 /* CacheKeyGenerator.swift */,
				C0C75B8D2C80F9A0FBA90BE1 /* CalendarExpansionAnimator.swift */,
				8601375ED9DDE972E08AC0FB /* DiskStorageManager.swift */,
				36DC5C478C9FA9ECC7F0D47F /* DoNotEatHelper.swift */,
				F7315F027BBD2E523243228C /* MealCutoffManager.swift */,
				6A2A3C01702A9D29F93ECE9D /* MealPlanDateRange.swift */,
				7D15F4CF782D3C898AF63928 /* MemoryManager.swift */,
				5034CD8F3B40B2B21EB5A035 /* NameCanonicalizer.swift */,
				8F0DEB57B33E9A9367B2B922 /* PerformanceMonitor.swift */,
				8F4864EDFD1DC93E9C72FC82 /* WeekTransitionAnimator.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		40D60551B89A5B09AD08F353 /* RecipeGenerator */ = {
			isa = PBXGroup;
			children = (
				2584E995A79858BB53E06153 /* GeneratedRecipeDetailView.swift */,
				9B388B3CA95BA26238131006 /* RecipeCardView.swift */,
				D6F89EB82B149F68E8B504FA /* RecipeGeneratorView.swift */,
				DF6E326DF3C70BF39313BBD0 /* RecipeGeneratorViewModel.swift */,
				60EB3E1F83D12B0C782679EA /* Components */,
				4D198BA74D55C5C5F2F97A2D /* Results */,
			);
			path = RecipeGenerator;
			sourceTree = "<group>";
		};
		43ADB630A1CFC6F77A402AED /* UserManagementTests */ = {
			isa = PBXGroup;
			children = (
				DD55B0B3D10F62784561A193 /* CrossDeviceSyncTests.swift */,
				BE644A19074040E7887A430A /* FoodPreferencesRegressionTests.swift */,
			);
			path = UserManagementTests;
			sourceTree = "<group>";
		};
		4D198BA74D55C5C5F2F97A2D /* Results */ = {
			isa = PBXGroup;
			children = (
				2C5D3C601DCB6D96733C2AF3 /* EnhancedRecipeCard.swift */,
				51F07E8F5B2F774E45C5AD58 /* GroupedResultsView.swift */,
				203CBC78A5DD35F560757709 /* QuickResultsView.swift */,
				1F1322C3C8EB8275E9862E22 /* RecipeCard.swift */,
			);
			path = Results;
			sourceTree = "<group>";
		};
		59898B4BE09F24676A966679 /* Profile */ = {
			isa = PBXGroup;
			children = (
				8A15779E849FB63339A45C31 /* DoNotEatViewTests.swift */,
			);
			path = Profile;
			sourceTree = "<group>";
		};
		6073F23F3DD4088E2B182FC6 /* Account */ = {
			isa = PBXGroup;
			children = (
				************************ /* AccountTelemetryHelper.swift */,
				************************ /* ChangePasswordView.swift */,
				D5D253BFFAEBCDFBB530B58D /* EditProfileView.swift */,
				B64D61BE29009F0227EA30F5 /* ProfileAccountView.swift */,
				2E7130D09787BD13C10ABAE1 /* UpdateEmailView.swift */,
			);
			path = Account;
			sourceTree = "<group>";
		};
		60EB3E1F83D12B0C782679EA /* Components */ = {
			isa = PBXGroup;
			children = (
				1A503727A85F13B70F4996A5 /* ConfigurationSummaryCard.swift */,
				49FCA1EACFEE3F7BA83C1151 /* GenerateButton.swift */,
				601D5438388977111C07153B /* ModeSelector.swift */,
				C0F436AB8DE0B60A131EAA60 /* PantryStateWarning.swift */,
				31E91787BB85372FF1F5C5E1 /* PlanSaveBannerView.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		72C95A01F0CC48629DF9E9B1 /* 2_Results */ = {
			isa = PBXGroup;
			children = (
				D561CE9DD4BF726A0517475C /* ProcessingScreen.swift */,
			);
			path = 2_Results;
			sourceTree = "<group>";
		};
		75DE33128F7D036CC2DBC4AA /* 3_Results */ = {
			isa = PBXGroup;
			children = (
				58F7E951E15E903C3F2DA88E /* ResultsView.swift */,
				B02FFC8EB3DFEB02AD57E0F2 /* ResultsViewModel.swift */,
			);
			path = 3_Results;
			sourceTree = "<group>";
		};
		777F36B8DA01C8AB0E61EDA1 /* Debug */ = {
			isa = PBXGroup;
			children = (
				79D1F4365F0010CABC1CADF7 /* DebugView.swift */,
				64498B16CC20397CD1568211 /* DebugViewModel.swift */,
			);
			path = Debug;
			sourceTree = "<group>";
		};
		777FE13A89F8A74C31140BD6 /* ManualTestScripts */ = {
			isa = PBXGroup;
			children = (
				7062A66F7F17C2328DC720D8 /* FamilyInfoManualTest.md */,
				CF9EE15071AC0CEEA67DC8EA /* SignInManualTest.md */,
			);
			path = ManualTestScripts;
			sourceTree = "<group>";
		};
		8AF856DE78DD25E25BA3690A /* Pantry */ = {
			isa = PBXGroup;
			children = (
				E09DF5AB9B43F2B5940B387E /* PantryView.swift */,
				D94EBA0AA79E38BB545F9F61 /* PantryViewModel.swift */,
			);
			path = Pantry;
			sourceTree = "<group>";
		};
		A1F9EAED5F5762EE342BAE4F /* Coordinator */ = {
			isa = PBXGroup;
			children = (
				7CAF8BF954BEC15E0AA2C354 /* AppCoordinator.swift */,
			);
			path = Coordinator;
			sourceTree = "<group>";
		};
		A3C06954CFBF169AC85CF7F6 /* Models */ = {
			isa = PBXGroup;
			children = (
				40BCDE3BE7D8C1EE496251EE /* DayPlan.swift */,
				15BA12893C06D41D7F40712F /* DisplayError.swift */,
				7BC9FDEE48F6CEFBC05DBDB3 /* FavoriteItem.swift */,
				74A0B7153B64EF215DE57ABB /* GenerationFingerprint.swift */,
				C354DB04ECE65CBF0CAE1FF5 /* Ingredient.swift */,
				D53F32D3F9209C84D51E3BB6 /* ManageAction.swift */,
				269ABED48A4B361CEFD34F9B /* MealPlan.swift */,
				8C68A9E936A7EAA7E7E43E81 /* MealPlanGenerationRequest.swift */,
				A9B923A799337A751F8CA037 /* MealSlot.swift */,
				1923DDF532C40ABB691EE4F4 /* MealSlotRequest.swift */,
				84B3CC06B1FB6E25E979F9D9 /* PantryCategory.swift */,
				1F608E2C70FC1D3D0EFE40EA /* QuickHistoryIndexEntry.swift */,
				ED1AFD29BA8ECAFC17F3BBAE /* QuickResultHistory.swift */,
				AE22904B43C38F5DA2C1BCCE /* Recipe.swift */,
				BFD881A4E73B5A047F08F6EB /* RecipeDetail.swift */,
				2D0D2194CA4CE570714310BB /* RecipeGenerationRequest.swift */,
				************************ /* RecipeGenerationTypes.swift */,
				00C6D31A5B45C429D2C7BC73 /* RecipeUIModel.swift */,
				55ACDE8516C85BC6B4D16CC7 /* RegenerateModeState.swift */,
				416FE6AD741760160A09D36A /* RegenerateRequest.swift */,
				9FD4633A895C41348C22C35D /* RemoteConfiguration.swift */,
				BAC67331A1B12A7A1522632C /* RetentionPolicy.swift */,
				633E17F5B1C449AB300F5F0F /* StorageConfiguration.swift */,
				D4867FFC3C03EA927A2A4619 /* SwiftDataModels.swift */,
				AB4EE67748A5F44F137361C2 /* UserPreferences.swift */,
				35CDF50BB12C724D6BBE8CC2 /* ViewState.swift */,
				72BDAC489562E49EF25728FB /* WeeklyMealPlan.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A8D2359556DF07234EA9B027 /* Tests */ = {
			isa = PBXGroup;
			children = (
				019EC1659BB4930E5F25FB68 /* AppRouteTests.swift */,
				291A9FD30D4F2B1F7BD287C9 /* AuthenticationIntegrationTests.swift */,
				B2A65866BDC4D464B9AA9889 /* AuthenticationServiceTests.swift */,
				DB5DFDC40745AC5FE1F38B7D /* AuthenticationTestConfig.swift */,
				66A68E1CF81B5597FEA5A6D6 /* CanGenerateLogicTests.swift */,
				8DCA11F0EFCAF5EEF83855C7 /* ConfigurationSummaryTests.swift */,
				B7C7801EFEC4593FB2232DE5 /* MealCutoffManagerTests.swift */,
				0A075453B11D3A164A272C03 /* MealPlanDateRangeTests.swift */,
				A8BF948EA96EBF61EA2C4D8E /* NavigationCoordinatorTests.swift */,
				6422EEE3D50DD3ABDC943DAC /* PantryDeletionTests.swift */,
				485125C0A942C587A2B3F368 /* PantryStateProviderTests.swift */,
				FA3EE027C1359C67EA6838E7 /* PlanStoreMergePolicyTests.swift */,
				18FFE68D22ADDE72EF29C790 /* QuickResultsViewSnapshotTests.swift */,
				712679BBC79E5B5AA28585E5 /* README.md */,
				A27432683AE4E32656D9D190 /* RecipeGeneratorTimeoutTests.swift */,
				BEEFC3C7A57736C5A6735DB9 /* RecipeGrouperTests.swift */,
				BE62E7F68F1DE3B693DC7B0F /* RecipeRequestBuilderTests.swift */,
				CD8E19A861A07FB044735E08 /* RecipeServiceAdapterTests.swift */,
				BE7313BDD6245F5968889E7A /* RemoteConfigurationManagerTests.swift */,
				4EAA0C9279CD7A5A60C41D4C /* TelemetryFeedbackTests.swift */,
				8A970C455DFCCEF754916B85 /* UserPreferencesCustomExclusionsTests.swift */,
				29E9A068D2D1CEC81FD1B865 /* ViewStateTests.swift */,
				B7EF8E0C141C1577A5274AB5 /* Features */,
				CD599AC700AA8ED54A665E93 /* Integration */,
				777FE13A89F8A74C31140BD6 /* ManualTestScripts */,
				43ADB630A1CFC6F77A402AED /* UserManagementTests */,
				E31F08BFD9D82F6F25A9F1F6 /* Utils */,
			);
			path = Tests;
			sourceTree = "<group>";
		};
		B4316D9D9DE845B78D7D90CD /* Reauthentication */ = {
			isa = PBXGroup;
			children = (
				6CF52E4781421CC9B5270212 /* ReauthenticationCoordinator.swift */,
				254B55743876E6E92DCC2F22 /* ReauthenticationView.swift */,
			);
			path = Reauthentication;
			sourceTree = "<group>";
		};
		B4B9DBFF954BD5347C879108 /* Features */ = {
			isa = PBXGroup;
			children = (
				1FC412429FB925EF376F80A6 /* 1_ImageCapture */,
				72C95A01F0CC48629DF9E9B1 /* 2_Results */,
				75DE33128F7D036CC2DBC4AA /* 3_Results */,
				777F36B8DA01C8AB0E61EDA1 /* Debug */,
				8AF856DE78DD25E25BA3690A /* Pantry */,
				3B995C1EF162DAD5B2491BF9 /* Profile */,
				40D60551B89A5B09AD08F353 /* RecipeGenerator */,
				E5390B68F1EF030BF301801A /* Recipes */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		B7EF8E0C141C1577A5274AB5 /* Features */ = {
			isa = PBXGroup;
			children = (
				59898B4BE09F24676A966679 /* Profile */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		BBEFB2E0D465492D3CC1B9F4 /* Application */ = {
			isa = PBXGroup;
			children = (
				9BF94B7ADBCEE860F7B723CC /* api-keys.plist */,
				3C6BAB000A706022E5DE4311 /* App.swift */,
				3C4267156C55BCA053A6D391 /* Assets.xcassets */,
				8AE611CBEB6F9C65CA8F9BF6 /* GoogleService-Info.plist */,
				176398FEADBD4CF189075941 /* Info.plist */,
				70D683F0BF9B783BB0F51EE6 /* IngredientScanner.entitlements */,
			);
			path = Application;
			sourceTree = "<group>";
		};
		C28058C6E36FC3F337B3F925 /* Utilities */ = {
			isa = PBXGroup;
			children = (
				A6E814F10D859D0594F1E3C2 /* APIKeys.swift */,
				F051D9F4E367F48AC03E0B61 /* Haptics.swift */,
				C093F1FD032970F0D124F474 /* ImagePicker.swift */,
				11DBEC63A8F70F25F4C5480C /* MultiImagePicker.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		CD599AC700AA8ED54A665E93 /* Integration */ = {
			isa = PBXGroup;
			children = (
				FF3330A24B7623BDBE3917DC /* DoNotEatMigrationTests.swift */,
			);
			path = Integration;
			sourceTree = "<group>";
		};
		D463D5725897FDF54E6EABBC = {
			isa = PBXGroup;
			children = (
				BBEFB2E0D465492D3CC1B9F4 /* Application */,
				A1F9EAED5F5762EE342BAE4F /* Coordinator */,
				B4B9DBFF954BD5347C879108 /* Features */,
				A3C06954CFBF169AC85CF7F6 /* Models */,
				E8F39B9E25349F9BC1411826 /* Services */,
				A8D2359556DF07234EA9B027 /* Tests */,
				C28058C6E36FC3F337B3F925 /* Utilities */,
				3D0BCE87402CE9603601FF27 /* Utils */,
				DD9AFF0A7B0103F96DA6D4F5 /* Views */,
				DDD72ED76DA11BC22FFC4895 /* Products */,
			);
			sourceTree = "<group>";
		};
		DA90FC9A3284D48373090E42 /* Feedback */ = {
			isa = PBXGroup;
			children = (
				9AD0A4EBB4C3D398D77A4D04 /* FeedbackBar.swift */,
			);
			path = Feedback;
			sourceTree = "<group>";
		};
		DBED30D210916EF42FB82FA2 /* Custom */ = {
			isa = PBXGroup;
			children = (
				897C9D4CC68AA9A30767B1C0 /* AdditionalRequestEditor.swift */,
				3C7CD7EB90744FA9A3D485D6 /* CuisinesChips.swift */,
				A8BE75E54566F07167996995 /* DaysSelector.swift */,
				8AA0B415236EF9F69BB30E35 /* MealConfigEditor.swift */,
				EA863EB201AE20151A867CAD /* MealSelector.swift */,
			);
			path = Custom;
			sourceTree = "<group>";
		};
		DD9AFF0A7B0103F96DA6D4F5 /* Views */ = {
			isa = PBXGroup;
			children = (
				74F9E581A20F80EA4479D452 /* CalendarMatrixView.swift */,
				38C4E523471A626055C6E95E /* CollapsedCalendarView.swift */,
				9A4AB7496EBE832DB8795330 /* ExpandedCalendarView.swift */,
				9C4917480BCB350A2F6FF644 /* FavoriteButton.swift */,
				356EEF6CAF37F5EDED9A0B5E /* FavoritesEmptyStateView.swift */,
				3F4007B12B610265FCE59E7F /* HorizontalQuickResultsView.swift */,
				D4E1A6DF07EA911BE766638A /* ManageSelectionView.swift */,
				F75A5AB95205838619B79FEE /* MealSlotIndicator.swift */,
				C7E604EC9950F933DD22D95A /* PageIndicatorView.swift */,
				D10DA713D4A39B43DBEA9C7E /* PlansEmptyStateView.swift */,
				F5D207A9E7FF3219859AD0FC /* QuickEmptyStateView.swift */,
				65151B8943DDA722BFA8A88F /* QuickResultCardView.swift */,
				4476EDC80C21FDEA1F3988E6 /* QuickResultPageView.swift */,
				B356A9042F89FA0965EF0447 /* RecipeCardHeaderView.swift */,
				7E3CD0B9F5B6D26D7A556D78 /* RecipeCompatibilityChips.swift */,
				1713B0A4BB0475F6E1513321 /* SwipeableMealPlanCalendarView.swift */,
				7B74D0BA67827234B1C6D87F /* WeekIndicatorView.swift */,
				F695708B4D4AC8B2495D5B67 /* Components */,
				DBED30D210916EF42FB82FA2 /* Custom */,
				DA90FC9A3284D48373090E42 /* Feedback */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		DDD72ED76DA11BC22FFC4895 /* Products */ = {
			isa = PBXGroup;
			children = (
				D0E63A4B380015E8E3BF68C8 /* IngredientScanner.app */,
				9BBD1837E2145AFF47B7839D /* IngredientScannerTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		E31F08BFD9D82F6F25A9F1F6 /* Utils */ = {
			isa = PBXGroup;
			children = (
				0B1553BA82F6EB30E6E2C6CB /* AsyncTimeoutTests.swift */,
				7A884A291FA4749F093A2A99 /* DoNotEatHelperTests.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		E5390B68F1EF030BF301801A /* Recipes */ = {
			isa = PBXGroup;
			children = (
				3D132F616063633C3CF22748 /* CapacityIndicatorView.swift */,
				F41F0DE8D133CECCA075986E /* FavoritesView.swift */,
				2BD0EE1A287D3518D7678381 /* MealPlanCalendarView.swift */,
				C2AD1028F7AB7D4ED582D627 /* PlansHistoryView.swift */,
				CBC17A041025DFCE982EE44F /* QuickHistoryView.swift */,
				4418C6EBABFCD397C6708DB9 /* RecipeHistoryTabView.swift */,
				98B8CC92CB9B5D9E94B23193 /* RecipesView.swift */,
				5DDDD7E86CC44475BCCD2D93 /* RecipesViewModel.swift */,
			);
			path = Recipes;
			sourceTree = "<group>";
		};
		E8F39B9E25349F9BC1411826 /* Services */ = {
			isa = PBXGroup;
			children = (
				1FAE924D1A86CB90B9B39DE0 /* AccountResetManager.swift */,
				59F5CB5CD990DF3795FBEA8D /* APIProtocols.swift */,
				2AE13D94EDA6D7C6CF36F861 /* AsyncImagePreparationService.swift */,
				002990977B33054F36A3499D /* AuthenticationService.swift */,
				618BC01DC46E43C192A972EA /* ConcurrencyManager.swift */,
				************************ /* ExpirationAlertManager.swift */,
				8B0B1D1EECDE31EBE316148E /* ExpirationNotificationManager.swift */,
				3DDEF4EBC4D5CA9A959AFA91 /* FavoritesManager.swift */,
				DBF14D5276C786D79A3296EA /* FavoritesStore.swift */,
				C0BF0FCC9FBAD1F55D36784E /* FirestoreError.swift */,
				96E57515DB53E2C4895273E7 /* GeminiAPIService.swift */,
				************************ /* GoogleVisionAPIService.swift */,
				74C9832A91945C87A0A70C4F /* HistoryStorageService.swift */,
				A6C33700D0F38BFB26D4F150 /* IngredientLibrary.swift */,
				C232D41B35C988DB1ED2C337 /* OptimizedNetworkService.swift */,
				DFC23B81C1836632A9C0C7D1 /* PantryService.swift */,
				BDAD84D6504FD42EA06C4AFD /* PantryStateProvider.swift */,
				9889DCAAFF16F7D88D2E2100 /* PlansRetentionManager.swift */,
				2F8D4B506DEF312B02334F4C /* PlanStore.swift */,
				9C4BB6A173DBA2A26848EBEB /* PreparedImage.swift */,
				7A3DBA7A6E753996362EB552 /* QuickHistoryManager.swift */,
				DD5874C638A51E908AA3C197 /* RecipeDetailCache.swift */,
				************************ /* RecipeGenerationService.swift */,
				561B59FB1C991C51A3AF05DB /* RecipeGrouper.swift */,
				FE2E2AED4CE768FFB8719125 /* RecipeRequestBuilder.swift */,
				D76E654E89C20F29237B9984 /* RecipeServiceAdapter.swift */,
				EACA497F9E6F77C9E4D26016 /* RemoteConfigurationManager.swift */,
				3CA085D99E48956579051C8A /* ServiceContainer.swift */,
				7AFCDFB98CA5D00C81D0A8D8 /* StructuredMealPlanGenerator.swift */,
				508254809ABE98C8FD2CD764 /* SummaryGenerator.swift */,
				81535A450F231A7A47014D5C /* SwiftDataStorageService.swift */,
				4BF734808AFF0C664EA566A1 /* TelemetryEventBuilder.swift */,
				67762E49563B93104A5F83EC /* TelemetryService.swift */,
				E45162584291B28B845255C8 /* UserProfileService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		F695708B4D4AC8B2495D5B67 /* Components */ = {
			isa = PBXGroup;
			children = (
				A0AE66C6EA17D96111A50370 /* ConfigurationSummary.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		930782F8BD78C73099163318 /* IngredientScanner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 934DAD8BBAF2712F704AEE6A /* Build configuration list for PBXNativeTarget "IngredientScanner" */;
			buildPhases = (
				ADD5E749E813581FAABB57D6 /* Sources */,
				D58938D13C4BC5DD9A99C61C /* Resources */,
				0786AF5BB94AD8A44FA2C5E8 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = IngredientScanner;
			packageProductDependencies = (
				ABE43B4277A5DC9DFB4721A7 /* FirebaseAuth */,
				B4B07A517E72BCF7376D449A /* FirebaseFirestore */,
				D59E35DCDAE975A10BC98623 /* FirebaseAnalytics */,
				C7CA2B5F33DE3455FFB8FD33 /* GoogleSignIn */,
			);
			productName = IngredientScanner;
			productReference = D0E63A4B380015E8E3BF68C8 /* IngredientScanner.app */;
			productType = "com.apple.product-type.application";
		};
		CE808712035F72B1A289D32E /* IngredientScannerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1BF8CA8768AFEA82F68A922D /* Build configuration list for PBXNativeTarget "IngredientScannerTests" */;
			buildPhases = (
				845C9129FE30BD3D240FE7A2 /* Sources */,
				12CEEFE85E13130C02CAE8E8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A637B6A81D4CAB4DCEA60131 /* PBXTargetDependency */,
			);
			name = IngredientScannerTests;
			packageProductDependencies = (
			);
			productName = IngredientScannerTests;
			productReference = 9BBD1837E2145AFF47B7839D /* IngredientScannerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2212BE8852EE267CB6C8FE89 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1430;
				TargetAttributes = {
					930782F8BD78C73099163318 = {
						DevelopmentTeam = "";
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 3F5BB50E25893847BAF617DA /* Build configuration list for PBXProject "IngredientScanner" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = D463D5725897FDF54E6EABBC;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				F03E3D36680729198EB6D5B2 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				79A7909AF8C1ADE48D12BC9C /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
			);
			preferredProjectObjectVersion = 77;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				930782F8BD78C73099163318 /* IngredientScanner */,
				CE808712035F72B1A289D32E /* IngredientScannerTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		12CEEFE85E13130C02CAE8E8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				5CD83495B880B4C371C615CB /* FamilyInfoManualTest.md in Resources */,
				C7D500DD1FECD01DC8676CBF /* README.md in Resources */,
				457A708AE6D7984C1639692F /* SignInManualTest.md in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D58938D13C4BC5DD9A99C61C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				190112BDFD568D7BD1682DCA /* Assets.xcassets in Resources */,
				929C5362403990D168CD15BC /* GoogleService-Info.plist in Resources */,
				BDBFE1EE64AC1ACE45B2E9E8 /* api-keys.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		845C9129FE30BD3D240FE7A2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				73ECF5365DD896E0F048344C /* AppRouteTests.swift in Sources */,
				422BFD7C7D9D1FC11CE51160 /* AsyncTimeoutTests.swift in Sources */,
				FFAE4C707F09293C4B89603B /* AuthenticationIntegrationTests.swift in Sources */,
				6208AEA3C909440C0BBFAEA3 /* AuthenticationServiceTests.swift in Sources */,
				7B02845B576A3CBC3ECA0C16 /* AuthenticationTestConfig.swift in Sources */,
				BD3AE20BD7773B82A26D4290 /* CanGenerateLogicTests.swift in Sources */,
				6124DD16BC643689DA54E870 /* ConfigurationSummaryTests.swift in Sources */,
				9AF8C2E290B054E66535276A /* CrossDeviceSyncTests.swift in Sources */,
				4364C48E93AFE8B0D52F6AA5 /* DoNotEatHelperTests.swift in Sources */,
				6AF505AB5588F7B5F05F763E /* DoNotEatMigrationTests.swift in Sources */,
				4FF6F4EDD06DBBE415C5A6BA /* DoNotEatViewTests.swift in Sources */,
				F55F90C52C302559DC2738DA /* FoodPreferencesRegressionTests.swift in Sources */,
				9B305BF7C2A046178293D6B1 /* MealCutoffManagerTests.swift in Sources */,
				DD9941CC6DE72C5723EE443C /* MealPlanDateRangeTests.swift in Sources */,
				097F1718E88068E43C52852C /* NavigationCoordinatorTests.swift in Sources */,
				D6212D7B0BB73FD279EB95E2 /* PantryDeletionTests.swift in Sources */,
				5BCCAB17E61CB7A78A253BAE /* PantryStateProviderTests.swift in Sources */,
				62B466E9316457EDA57DBC06 /* PlanStoreMergePolicyTests.swift in Sources */,
				4CADE21C3557740DC5825721 /* QuickResultsViewSnapshotTests.swift in Sources */,
				F43375FB4DB343221C759399 /* RecipeGeneratorTimeoutTests.swift in Sources */,
				20D130C30EEC632A98A79BA2 /* RecipeGrouperTests.swift in Sources */,
				6D0DB81AB702D79AA5385267 /* RecipeRequestBuilderTests.swift in Sources */,
				7101622282493DC519FE8A2B /* RecipeServiceAdapterTests.swift in Sources */,
				5CD42B751983ECB910C2409B /* RemoteConfigurationManagerTests.swift in Sources */,
				4240ECF855067C42906AAAAC /* TelemetryFeedbackTests.swift in Sources */,
				7FE8F41672ECA0D3AB9FD062 /* UserPreferencesCustomExclusionsTests.swift in Sources */,
				B3D4185BDA01F72DC0980B28 /* ViewStateTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ADD5E749E813581FAABB57D6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				5ED14FC0EC3F3AA2E63FD535 /* APIKeys.swift in Sources */,
				F5CE6AFD5A60E98F7FA9586E /* APIProtocols.swift in Sources */,
				34B6D7AA331365DFD6C0948F /* AccountResetManager.swift in Sources */,
				C1A180C6BD50C3FA1A4D4F74 /* AccountTelemetryHelper.swift in Sources */,
				849D023AE6ECC7FC7E614880 /* AdditionalRequestEditor.swift in Sources */,
				78C944EA1DE275CA98A3A83A /* AllergiesIntolerancesView.swift in Sources */,
				470B030CFE6F3D8EF8AF9C97 /* App.swift in Sources */,
				88D2F764123B52301A3B70B8 /* AppCoordinator.swift in Sources */,
				728037D12D75D4B900849FE8 /* AsyncImagePreparationService.swift in Sources */,
				350020676BBFA2E592C4CE7A /* AuthenticationService.swift in Sources */,
				5959F28A176F961F1CB92521 /* BatchOperationManager.swift in Sources */,
				D848379A5A753AB625F95407 /* CacheKeyGenerator.swift in Sources */,
				73567D2A64329FD092A07A09 /* CalendarExpansionAnimator.swift in Sources */,
				8501C5B848616AB2290EF198 /* CalendarMatrixView.swift in Sources */,
				DCD621CA55AD07654E60141C /* CapacityIndicatorView.swift in Sources */,
				6D79A5C099F8A11F3622CE42 /* ChangePasswordView.swift in Sources */,
				0D87B0BAB324F3C46E9A549F /* CollapsedCalendarView.swift in Sources */,
				1C514D8C68BD8C0DA425F4B0 /* ConcurrencyManager.swift in Sources */,
				1E3C025D2108AC511947205F /* ConfigurationSummary.swift in Sources */,
				A13ABB8AE497C0CF26649E19 /* ConfigurationSummaryCard.swift in Sources */,
				1F8E6BE803A8940A5599CAC2 /* CuisinesChips.swift in Sources */,
				11078A23C9E7C54B3BE9DD28 /* CustomItemEntrySheet.swift in Sources */,
				E6AFDE8843CE77D77D18BF41 /* DayPlan.swift in Sources */,
				13C3BFA5787D126B76F9B222 /* DaysSelector.swift in Sources */,
				89AFB66F259BE7565A5D1801 /* DebugView.swift in Sources */,
				6C6A417E7F5E0972303323D6 /* DebugViewModel.swift in Sources */,
				5C0A0B8D64D2DE136D8CBE73 /* DietaryRestrictionsView.swift in Sources */,
				65BA3ABDC4AA873F2FBCD75B /* DiskStorageManager.swift in Sources */,
				AF1C7E872F143AB22E28AFEB /* DisplayError.swift in Sources */,
				2EFBF9ADB301832F959B5675 /* DoNotEatHelpSheet.swift in Sources */,
				3F5F6048E048F4323F9806C6 /* DoNotEatHelper.swift in Sources */,
				728D2FC9A70A90505EFEA9B2 /* DoNotEatSelectionStore.swift in Sources */,
				BE61FB414F25F66B2C608709 /* DoNotEatView.swift in Sources */,
				B37EE88C3B635CDF91F452AA /* EditProfileView.swift in Sources */,
				56D28E25E361C9BED25A9AE7 /* EnhancedRecipeCard.swift in Sources */,
				AEE1492DE07FCE6AEB73AC6A /* EquipmentChipsView.swift in Sources */,
				076E088D06AEA68BF880E443 /* EquipmentView.swift in Sources */,
				1602CCC1FCD042DA353BFB5D /* ErrorView.swift in Sources */,
				305107201B6848AF340565CE /* ExpandedCalendarView.swift in Sources */,
				546FD3797670C44238850D6F /* ExpirationAlertManager.swift in Sources */,
				3E55F8A163B930B0C10DF7C3 /* ExpirationNotificationManager.swift in Sources */,
				83EC4916030D1DE92209B3FB /* FamilySizeView.swift in Sources */,
				F0D28AA28203A45724F3BF5A /* FavoriteButton.swift in Sources */,
				45DC5B2DB5B5996098079705 /* FavoriteItem.swift in Sources */,
				691855AD7EB52D27B499BC9D /* FavoritesEmptyStateView.swift in Sources */,
				CFFD729D8B69158EC7D15475 /* FavoritesManager.swift in Sources */,
				57C0F9F47D4919B96AB3C9A9 /* FavoritesStore.swift in Sources */,
				FF928F3C52B45DF86B4B9A4E /* FavoritesView.swift in Sources */,
				93B3D23D7EC43832779112E6 /* FeedbackBar.swift in Sources */,
				9F593E6A75E3601FB6CA8770 /* FirestoreError.swift in Sources */,
				590EF66376A8348CBBBF4BCF /* GeminiAPIService.swift in Sources */,
				82CFCA724ABD5520D70A210B /* GenerateButton.swift in Sources */,
				46D1AA5FBF4641ECC38CAB93 /* GeneratedRecipeDetailView.swift in Sources */,
				331CC5F75E97D39B88BD021E /* GenerationFingerprint.swift in Sources */,
				E684CC98841467E1D92339FE /* GoogleVisionAPIService.swift in Sources */,
				11C58082195D9D6D63494CFC /* GroupedResultsView.swift in Sources */,
				C93D1E772CC97F0DFA65CD27 /* Haptics.swift in Sources */,
				F56C53600070BF9D777511E9 /* HistoryStorageService.swift in Sources */,
				717C81031AC06D5ECFDF445C /* HorizontalQuickResultsView.swift in Sources */,
				16947ACBFFE296C4C5608521 /* ImagePicker.swift in Sources */,
				9AC0D19AA92FEA4636B57E8D /* Ingredient.swift in Sources */,
				F34EAA77130AA93558A20E26 /* IngredientLibrary.swift in Sources */,
				456E8CB6D048D357117FDAE1 /* LoadingView.swift in Sources */,
				C0F0F6890E1A023460BE88CA /* ManageAction.swift in Sources */,
				3D85C450419AD2C6B5F04794 /* ManageSelectionView.swift in Sources */,
				9D939EC81F1DF782C86A5704 /* MealConfigEditor.swift in Sources */,
				5A9EB1ABF0F85640272D1540 /* MealCutoffManager.swift in Sources */,
				25CEE0B18FF5D33FBB0A3CDA /* MealPlan.swift in Sources */,
				759EE056788D582FC3FE6155 /* MealPlanCalendarView.swift in Sources */,
				1390FD32E792A0A9719D78FC /* MealPlanDateRange.swift in Sources */,
				BB8716DF559A38032C1206B2 /* MealPlanGenerationRequest.swift in Sources */,
				B1049F793832B7463DCDED70 /* MealSelector.swift in Sources */,
				************************ /* MealSlot.swift in Sources */,
				DFFDB75459955E361F07758E /* MealSlotIndicator.swift in Sources */,
				7D9460420F538FFBCF21FFDD /* MealSlotRequest.swift in Sources */,
				FFDEDB4FB90539DCE232F105 /* MemoryManager.swift in Sources */,
				9980C88FF2EF298D70231142 /* ModeSelector.swift in Sources */,
				3BF985B6E33165CCCAB97988 /* MultiImagePicker.swift in Sources */,
				22A148ED2EDA0D9F1398D8EB /* NameCanonicalizer.swift in Sources */,
				EFEA08C3450FB79F8941B2F8 /* NotificationsView.swift in Sources */,
				BF4FB6EDD3523DB2EA76C06B /* OptimizedNetworkService.swift in Sources */,
				79E9C04FF0513FA36BBB5FCF /* PageIndicatorView.swift in Sources */,
				465AA90B58DBF295DF9729FB /* PantryCategory.swift in Sources */,
				3CEEAA16C0A38AD1F62E30E0 /* PantryService.swift in Sources */,
				4DDFE5D0B3F7B40FEFBA78BD /* PantryStateProvider.swift in Sources */,
				1A3D422E366B31A3F4C92CA7 /* PantryStateWarning.swift in Sources */,
				9634C6A0A790DFCF8DB74308 /* PantryView.swift in Sources */,
				2009917FD1AE77EF5C26151D /* PantryViewModel.swift in Sources */,
				F0EE2FFD7ABAD7F6A56C5E87 /* PerformanceMonitor.swift in Sources */,
				8FFBEFCF2D0B8B70C7A25204 /* PlanSaveBannerView.swift in Sources */,
				CC1C55DA7952A3D044AC8245 /* PlanStore.swift in Sources */,
				E349857AFEDCC9D013E68A7D /* PlansEmptyStateView.swift in Sources */,
				E3BCB02F70895E97E5DFDD42 /* PlansHistoryView.swift in Sources */,
				************************ /* PlansRetentionManager.swift in Sources */,
				5CDD243A8D50CF3B1D79508C /* PreferencesEditView.swift in Sources */,
				84995C5F1A57FF917F21854C /* PreparedImage.swift in Sources */,
				DC6AC64160026E6BE9CA4292 /* ProcessingScreen.swift in Sources */,
				E9AE0994D018F35608020BE9 /* ProfileAccountView.swift in Sources */,
				5FA62A0FB480B7DD1ACFFBFC /* ProfileView.swift in Sources */,
				103EBCFA20A105DBA29F06BA /* QuickEmptyStateView.swift in Sources */,
				41B88EA8A705136D011C3957 /* QuickHistoryIndexEntry.swift in Sources */,
				10DD97B2DD408A1AB1CA7FA2 /* QuickHistoryManager.swift in Sources */,
				EA8F89BE23C5724048B3613B /* QuickHistoryView.swift in Sources */,
				397983BFA3B1C61BAAEA98ED /* QuickResultCardView.swift in Sources */,
				9175FB057F6581A929B418B9 /* QuickResultHistory.swift in Sources */,
				E8575D4C691F3DA906C99D8E /* QuickResultPageView.swift in Sources */,
				257DCFADF4F8AA5375CEAAE6 /* QuickResultsView.swift in Sources */,
				FDE88250E2BBE576907DC109 /* ReauthenticationCoordinator.swift in Sources */,
				332DCFF043824410C8B1D0CB /* ReauthenticationView.swift in Sources */,
				286D3AD60207B07E1922ED46 /* Recipe.swift in Sources */,
				D2CB3623DB80D90596DFAFB8 /* RecipeCard.swift in Sources */,
				39A499A7CDD24F1909081748 /* RecipeCardHeaderView.swift in Sources */,
				9AAB43E55D0D950A9774FB0D /* RecipeCardView.swift in Sources */,
				A8EE005EC0421B741022FAE5 /* RecipeCompatibilityChips.swift in Sources */,
				D86249C65F27BEE3D39041E4 /* RecipeDetail.swift in Sources */,
				04F4BC4DFB7E9DCA9E050276 /* RecipeDetailCache.swift in Sources */,
				EEED6D2A397A1A7701CA5D9C /* RecipeGenerationRequest.swift in Sources */,
				EC9C0184D659F23C2A4D1112 /* RecipeGenerationService.swift in Sources */,
				AC5A62C1B1C0B0F554DC12F6 /* RecipeGenerationTypes.swift in Sources */,
				EDB1FCBE871A2FC42E1BB1B0 /* RecipeGeneratorView.swift in Sources */,
				E6D16E11F54A7B3AC607A39C /* RecipeGeneratorViewModel.swift in Sources */,
				10DB2E5C2CC15BC608773B0B /* RecipeGrouper.swift in Sources */,
				4455621856F29081BB21E37C /* RecipeHistoryTabView.swift in Sources */,
				8E3182790C46308A760BF69D /* RecipeRequestBuilder.swift in Sources */,
				DEFA83DECCA0F8397BBA36DA /* RecipeServiceAdapter.swift in Sources */,
				2CD4FB43C1F24D0A6C270602 /* RecipeUIModel.swift in Sources */,
				3BBAF884691CE8BCAC30AFE1 /* RecipesView.swift in Sources */,
				F97E1CE239E7C4724DABF792 /* RecipesViewModel.swift in Sources */,
				D2694E9BF7B54C29F8F913BE /* RegenerateModeState.swift in Sources */,
				11F4D8DE14658CEE89A81B24 /* RegenerateRequest.swift in Sources */,
				40FEB17B88C5F3765C569E97 /* RemoteConfiguration.swift in Sources */,
				73076B2B7A47E522AAECBD7F /* RemoteConfigurationManager.swift in Sources */,
				300FF772D541C1E8D839037D /* ResultsView.swift in Sources */,
				4CAC1DE461273018EEC02DA7 /* ResultsViewModel.swift in Sources */,
				3D9558C8668010FB44D3979D /* RetentionPolicy.swift in Sources */,
				F9A41FBACF9CCA4A42169055 /* ServiceContainer.swift in Sources */,
				9D480EEED0BF61466A814CC5 /* SettingsView.swift in Sources */,
				45D2DE7DBB798692C6F82957 /* SignInView.swift in Sources */,
				7A3011B5F41A0480B47E8D1C /* StagingView.swift in Sources */,
				8C6699499ED137FE3BBEDE00 /* StagingViewModel.swift in Sources */,
				F0CB0A2302140E99F85EF1E2 /* StorageConfiguration.swift in Sources */,
				9B8AA968C7F1DBC83CB101DD /* StrictExclusionsView.swift in Sources */,
				9EEE4E086180E916D0C8124F /* StructuredMealPlanGenerator.swift in Sources */,
				76672B856A0F24B02726596D /* SummaryGenerator.swift in Sources */,
				3AFFA6126A9555091B68EAC6 /* SwiftDataModels.swift in Sources */,
				74F5ADC7DEBC005347DFF87A /* SwiftDataStorageService.swift in Sources */,
				0CCE064221711B63365F6960 /* SwipeableMealPlanCalendarView.swift in Sources */,
				BE82E83A77935AA118FD1AA6 /* TelemetryEventBuilder.swift in Sources */,
				FF18CF88C55BAAED3384A5A3 /* TelemetryService.swift in Sources */,
				4FD023AD5DA226B136291273 /* ToastView.swift in Sources */,
				89E02C1AE29DDB53BA692610 /* UpdateEmailView.swift in Sources */,
				A9EE505F6BA66751830BC279 /* UserPreferences.swift in Sources */,
				D79545A53384C3A69227721F /* UserProfileService.swift in Sources */,
				405B7FF8557B6E9D50708FB4 /* ViewState.swift in Sources */,
				E1221CAAC6402BD0D825E9EC /* WeekIndicatorView.swift in Sources */,
				685D6F2A91A1502EAD217297 /* WeekTransitionAnimator.swift in Sources */,
				0CC8C18E102CE54D96BE07F2 /* WeeklyMealPlan.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		A637B6A81D4CAB4DCEA60131 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 930782F8BD78C73099163318 /* IngredientScanner */;
			targetProxy = 466BBAE823935D9CC313E6A7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		346459FDE001E5157EE335D7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.kuo.IngredientScannerTests;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/IngredientScanner.app/IngredientScanner";
			};
			name = Debug;
		};
		A880B49E585BD8E0F7594CBB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ALLOW_ENTITLEMENTS_MODIFICATION = YES;
				CODE_SIGN_ENTITLEMENTS = Application/IngredientScanner.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				INFOPLIST_FILE = Application/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.kuo.ingredientscannertemp;
				PRODUCT_NAME = IngredientScanner;
				SDKROOT = iphoneos;
				SWIFT_STRICT_CONCURRENCY = complete;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		B03696EE21174C7DACC19C30 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ALLOW_ENTITLEMENTS_MODIFICATION = YES;
				CODE_SIGN_ENTITLEMENTS = Application/IngredientScanner.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				INFOPLIST_FILE = Application/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.kuo.ingredientscannertemp;
				PRODUCT_NAME = IngredientScanner;
				SDKROOT = iphoneos;
				SWIFT_STRICT_CONCURRENCY = complete;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		CB126F4D63CF17BE103E5043 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.kuo.IngredientScannerTests;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/IngredientScanner.app/IngredientScanner";
			};
			name = Release;
		};
		E99B563E6A920425D0D3542F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		F0F865AAC6BB2C1182B5E8D8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1BF8CA8768AFEA82F68A922D /* Build configuration list for PBXNativeTarget "IngredientScannerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				346459FDE001E5157EE335D7 /* Debug */,
				CB126F4D63CF17BE103E5043 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		3F5BB50E25893847BAF617DA /* Build configuration list for PBXProject "IngredientScanner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F0F865AAC6BB2C1182B5E8D8 /* Debug */,
				E99B563E6A920425D0D3542F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		934DAD8BBAF2712F704AEE6A /* Build configuration list for PBXNativeTarget "IngredientScanner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B03696EE21174C7DACC19C30 /* Debug */,
				A880B49E585BD8E0F7594CBB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		79A7909AF8C1ADE48D12BC9C /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
		F03E3D36680729198EB6D5B2 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		ABE43B4277A5DC9DFB4721A7 /* FirebaseAuth */ = {
			isa = XCSwiftPackageProductDependency;
			package = F03E3D36680729198EB6D5B2 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAuth;
		};
		B4B07A517E72BCF7376D449A /* FirebaseFirestore */ = {
			isa = XCSwiftPackageProductDependency;
			package = F03E3D36680729198EB6D5B2 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseFirestore;
		};
		C7CA2B5F33DE3455FFB8FD33 /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A7909AF8C1ADE48D12BC9C /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		D59E35DCDAE975A10BC98623 /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = F03E3D36680729198EB6D5B2 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 2212BE8852EE267CB6C8FE89 /* Project object */;
}
